{"name": "wolf_fun", "version": "1.0.0", "main": "dist/app.js", "scripts": {"dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "build": "cross-env NODE_ENV=production tsc", "build:dev": "cross-env NODE_ENV=development tsc", "start": "cross-env NODE_ENV=production node dist/app.js", "start:dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "seed:tasks": "npx sequelize-cli db:migrate && npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "migrate:offline-rewards": "node run_sequelize_migration.js", "migrate:offline-rewards:rollback": "node rollback_sequelize_migration.js", "debug:phrs-monitor": "ts-node src/scripts/debugPhrsMonitor.ts", "check:phrs-service": "ts-node src/scripts/checkPhrsService.ts", "test:phrs-monitoring": "ts-node src/scripts/testPhrsMonitoring.ts", "check:specific-block": "ts-node src/scripts/checkSpecificBlock.ts", "check:unregistered-deposits": "ts-node src/scripts/checkUnregisteredDeposits.ts", "handle:unregistered-deposits": "ts-node src/scripts/handleUnregisteredDeposits.ts", "migrate:offline-rewards:verify": "node verify_migration.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:phrs": "./scripts/test-phrs-system.sh", "test:contracts": "cd contracts && npm test", "test:api": "jest src/tests --testPathIgnorePatterns=integration", "test:integration": "jest src/tests/integration"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "dependencies": {"@ton/core": "^0.59.1", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.1.0", "@tonconnect/ui-react": "^2.0.11", "@types/node-polyglot": "^2.5.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "ajv-i18n": "^4.2.0", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "bullmq": "^5.41.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "ethers": "^6.14.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "grammy": "^1.35.1", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.12.0", "node-cron": "^3.0.3", "node-polyglot": "^2.6.0", "nodemailer": "^6.10.0", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "uuid": "^11.0.5", "zod": "^3.24.1"}, "devDependencies": {"@types/bignumber.js": "^4.0.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/glob": "^8.1.0", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.13.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.12", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}