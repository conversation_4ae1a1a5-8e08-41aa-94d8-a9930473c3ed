'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 修改 walletId 字段，允许为空
    await queryInterface.changeColumn('phrs_deposits', 'walletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: true, // 允许为空
      references: {
        model: 'user_wallets',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL' // 当用户钱包被删除时，设置为NULL而不是删除记录
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 回滚：将 walletId 字段改回不允许为空
    // 注意：这可能会失败，如果表中有NULL值
    await queryInterface.changeColumn('phrs_deposits', 'walletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'user_wallets',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });
  }
};
