const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("开始部署PHRS充值合约...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // 合约参数配置
  const PHRS_TOKEN_ADDRESS = process.env.PHRS_TOKEN_ADDRESS;
  const MIN_DEPOSIT_AMOUNT = ethers.parseEther(process.env.MIN_DEPOSIT_AMOUNT || "1"); // 默认1 PHRS
  const MAX_DEPOSIT_AMOUNT = ethers.parseEther(process.env.MAX_DEPOSIT_AMOUNT || "10000"); // 默认10000 PHRS

  if (!PHRS_TOKEN_ADDRESS) {
    throw new Error("请设置PHRS_TOKEN_ADDRESS环境变量");
  }

  console.log("合约参数:");
  console.log("- PHRS代币地址:", PHRS_TOKEN_ADDRESS);
  console.log("- 最小充值金额:", ethers.formatEther(MIN_DEPOSIT_AMOUNT), "PHRS");
  console.log("- 最大充值金额:", ethers.formatEther(MAX_DEPOSIT_AMOUNT), "PHRS");

  // 部署合约
  const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");
  const contract = await PHRSDepositContract.deploy(
    PHRS_TOKEN_ADDRESS,
    MIN_DEPOSIT_AMOUNT,
    MAX_DEPOSIT_AMOUNT
  );

  await contract.waitForDeployment();
  const contractAddress = await contract.getAddress();

  console.log("✅ PHRS充值合约部署成功!");
  console.log("合约地址:", contractAddress);
  console.log("交易哈希:", contract.deploymentTransaction().hash);

  // 验证合约部署
  console.log("\n验证合约部署...");
  const contractInfo = await contract.getContractInfo();
  console.log("合约信息验证:");
  console.log("- PHRS代币地址:", contractInfo[0]);
  console.log("- 最小充值金额:", ethers.formatEther(contractInfo[1]), "PHRS");
  console.log("- 最大充值金额:", ethers.formatEther(contractInfo[2]), "PHRS");
  console.log("- 总充值次数:", contractInfo[3].toString());
  console.log("- 总充值金额:", ethers.formatEther(contractInfo[4]), "PHRS");

  // 保存部署信息
  const deploymentInfo = {
    network: hre.network.name,
    contractAddress: contractAddress,
    deployerAddress: deployer.address,
    phrsTokenAddress: PHRS_TOKEN_ADDRESS,
    minDepositAmount: ethers.formatEther(MIN_DEPOSIT_AMOUNT),
    maxDepositAmount: ethers.formatEther(MAX_DEPOSIT_AMOUNT),
    deploymentTime: new Date().toISOString(),
    transactionHash: contract.deploymentTransaction().hash,
    blockNumber: contract.deploymentTransaction().blockNumber
  };

  const deploymentPath = path.join(__dirname, "..", "deployments");
  if (!fs.existsSync(deploymentPath)) {
    fs.mkdirSync(deploymentPath, { recursive: true });
  }

  const deploymentFile = path.join(deploymentPath, `${hre.network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

  console.log(`\n部署信息已保存到: ${deploymentFile}`);

  // 生成ABI文件
  const artifactPath = path.join(__dirname, "..", "artifacts", "contracts", "PHRSDepositContract.sol", "PHRSDepositContract.json");
  if (fs.existsSync(artifactPath)) {
    const artifact = JSON.parse(fs.readFileSync(artifactPath, "utf8"));
    const abiPath = path.join(deploymentPath, `PHRSDepositContract-${hre.network.name}.abi.json`);
    fs.writeFileSync(abiPath, JSON.stringify(artifact.abi, null, 2));
    console.log(`ABI文件已保存到: ${abiPath}`);
  }

  console.log("\n🎉 部署完成!");
  console.log("请将以下信息添加到后端环境变量:");
  console.log(`PHRS_DEPOSIT_CONTRACT_ADDRESS=${contractAddress}`);
  console.log(`PHRS_TOKEN_ADDRESS=${PHRS_TOKEN_ADDRESS}`);
  
  if (hre.network.name !== "hardhat") {
    console.log("\n等待区块确认后，可以使用以下命令验证合约:");
    console.log(`npx hardhat verify --network ${hre.network.name} ${contractAddress} "${PHRS_TOKEN_ADDRESS}" "${MIN_DEPOSIT_AMOUNT}" "${MAX_DEPOSIT_AMOUNT}"`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("部署失败:", error);
    process.exit(1);
  });
