const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("PHRSDepositContract", function () {
  let phrsToken;
  let depositContract;
  let owner;
  let user1;
  let user2;
  let addrs;

  const MIN_DEPOSIT = ethers.parseEther("1");
  const MAX_DEPOSIT = ethers.parseEther("10000");
  const INITIAL_SUPPLY = ethers.parseEther("1000000");

  beforeEach(async function () {
    [owner, user1, user2, ...addrs] = await ethers.getSigners();

    // 部署模拟PHRS代币合约
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    phrsToken = await MockERC20.deploy("PHRS Token", "PHRS", INITIAL_SUPPLY);
    await phrsToken.waitForDeployment();

    // 部署充值合约
    const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");
    depositContract = await PHRSDepositContract.deploy(
      await phrsToken.getAddress(),
      MIN_DEPOSIT,
      MAX_DEPOSIT
    );
    await depositContract.waitForDeployment();

    // 给用户分发代币
    await phrsToken.transfer(user1.address, ethers.parseEther("10000"));
    await phrsToken.transfer(user2.address, ethers.parseEther("10000"));
  });

  describe("部署", function () {
    it("应该正确设置初始参数", async function () {
      const contractInfo = await depositContract.getContractInfo();
      expect(contractInfo[0]).to.equal(await phrsToken.getAddress());
      expect(contractInfo[1]).to.equal(MIN_DEPOSIT);
      expect(contractInfo[2]).to.equal(MAX_DEPOSIT);
      expect(contractInfo[3]).to.equal(0); // totalDepositCount
      expect(contractInfo[4]).to.equal(0); // totalDepositAmount
    });

    it("应该拒绝无效的构造参数", async function () {
      const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");
      
      // 无效的代币地址
      await expect(
        PHRSDepositContract.deploy(ethers.ZeroAddress, MIN_DEPOSIT, MAX_DEPOSIT)
      ).to.be.revertedWithCustomError(depositContract, "InvalidAddress");

      // 无效的最小金额
      await expect(
        PHRSDepositContract.deploy(await phrsToken.getAddress(), 0, MAX_DEPOSIT)
      ).to.be.revertedWithCustomError(depositContract, "InvalidAmount");

      // 最大金额小于等于最小金额
      await expect(
        PHRSDepositContract.deploy(await phrsToken.getAddress(), MAX_DEPOSIT, MIN_DEPOSIT)
      ).to.be.revertedWithCustomError(depositContract, "InvalidAmount");
    });
  });

  describe("充值功能", function () {
    it("应该允许有效的充值", async function () {
      const depositAmount = ethers.parseEther("100");
      
      // 授权代币
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount);
      
      // 执行充值
      await expect(depositContract.connect(user1).deposit(depositAmount))
        .to.emit(depositContract, "Deposit")
        .withArgs(user1.address, depositAmount, anyValue, 0);

      // 验证状态更新
      expect(await depositContract.userTotalDeposits(user1.address)).to.equal(depositAmount);
      expect(await depositContract.totalDepositCount()).to.equal(1);
      expect(await depositContract.totalDepositAmount()).to.equal(depositAmount);
      expect(await depositContract.getUserDepositCount(user1.address)).to.equal(1);
    });

    it("应该拒绝金额为0的充值", async function () {
      await expect(
        depositContract.connect(user1).deposit(0)
      ).to.be.revertedWithCustomError(depositContract, "InvalidAmount");
    });

    it("应该拒绝小于最小金额的充值", async function () {
      const smallAmount = ethers.parseEther("0.5");
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), smallAmount);
      
      await expect(
        depositContract.connect(user1).deposit(smallAmount)
      ).to.be.revertedWithCustomError(depositContract, "AmountTooSmall");
    });

    it("应该拒绝大于最大金额的充值", async function () {
      const largeAmount = ethers.parseEther("20000");
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), largeAmount);
      
      await expect(
        depositContract.connect(user1).deposit(largeAmount)
      ).to.be.revertedWithCustomError(depositContract, "AmountTooLarge");
    });

    it("应该拒绝未授权的充值", async function () {
      const depositAmount = ethers.parseEther("100");
      
      await expect(
        depositContract.connect(user1).deposit(depositAmount)
      ).to.be.reverted;
    });

    it("应该支持多次充值", async function () {
      const depositAmount1 = ethers.parseEther("100");
      const depositAmount2 = ethers.parseEther("200");
      
      // 第一次充值
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount1);
      await depositContract.connect(user1).deposit(depositAmount1);
      
      // 第二次充值
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount2);
      await depositContract.connect(user1).deposit(depositAmount2);

      // 验证状态
      expect(await depositContract.userTotalDeposits(user1.address)).to.equal(depositAmount1 + depositAmount2);
      expect(await depositContract.getUserDepositCount(user1.address)).to.equal(2);
      expect(await depositContract.totalDepositCount()).to.equal(2);
    });
  });

  describe("查询功能", function () {
    beforeEach(async function () {
      // 准备测试数据
      const amount1 = ethers.parseEther("100");
      const amount2 = ethers.parseEther("200");
      
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), amount1);
      await depositContract.connect(user1).deposit(amount1);
      
      await phrsToken.connect(user2).approve(await depositContract.getAddress(), amount2);
      await depositContract.connect(user2).deposit(amount2);
    });

    it("应该正确返回用户充值记录", async function () {
      const record = await depositContract.getUserDeposit(user1.address, 0);
      expect(record.user).to.equal(user1.address);
      expect(record.amount).to.equal(ethers.parseEther("100"));
      expect(record.timestamp).to.be.gt(0);
    });

    it("应该正确返回批量用户充值记录", async function () {
      const records = await depositContract.getUserDeposits(user1.address, 0, 10);
      expect(records.length).to.equal(1);
      expect(records[0].user).to.equal(user1.address);
      expect(records[0].amount).to.equal(ethers.parseEther("100"));
    });

    it("应该正确返回全局充值记录", async function () {
      const record = await depositContract.getDeposit(0);
      expect(record.user).to.equal(user1.address);
      expect(record.amount).to.equal(ethers.parseEther("100"));
    });

    it("应该正确返回批量全局充值记录", async function () {
      const records = await depositContract.getDeposits(0, 10);
      expect(records.length).to.equal(2);
      expect(records[0].user).to.equal(user1.address);
      expect(records[1].user).to.equal(user2.address);
    });
  });

  describe("管理员功能", function () {
    it("应该允许管理员更新最小充值金额", async function () {
      const newMinAmount = ethers.parseEther("2");
      
      await expect(depositContract.setMinDepositAmount(newMinAmount))
        .to.emit(depositContract, "MinDepositAmountUpdated")
        .withArgs(MIN_DEPOSIT, newMinAmount);
      
      const contractInfo = await depositContract.getContractInfo();
      expect(contractInfo[1]).to.equal(newMinAmount);
    });

    it("应该允许管理员更新最大充值金额", async function () {
      const newMaxAmount = ethers.parseEther("20000");
      
      await expect(depositContract.setMaxDepositAmount(newMaxAmount))
        .to.emit(depositContract, "MaxDepositAmountUpdated")
        .withArgs(MAX_DEPOSIT, newMaxAmount);
      
      const contractInfo = await depositContract.getContractInfo();
      expect(contractInfo[2]).to.equal(newMaxAmount);
    });

    it("应该拒绝非管理员更新参数", async function () {
      await expect(
        depositContract.connect(user1).setMinDepositAmount(ethers.parseEther("2"))
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("应该允许管理员暂停和恢复合约", async function () {
      await depositContract.pause();
      
      const depositAmount = ethers.parseEther("100");
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount);
      
      await expect(
        depositContract.connect(user1).deposit(depositAmount)
      ).to.be.revertedWith("Pausable: paused");
      
      await depositContract.unpause();
      
      await expect(depositContract.connect(user1).deposit(depositAmount))
        .to.emit(depositContract, "Deposit");
    });

    it("应该允许管理员紧急提取代币", async function () {
      // 先充值一些代币到合约
      const depositAmount = ethers.parseEther("100");
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount);
      await depositContract.connect(user1).deposit(depositAmount);
      
      // 紧急提取
      await expect(
        depositContract.emergencyWithdraw(
          await phrsToken.getAddress(),
          depositAmount,
          owner.address
        )
      ).to.emit(depositContract, "EmergencyWithdraw")
        .withArgs(await phrsToken.getAddress(), depositAmount, owner.address);
    });
  });

  describe("重入攻击防护", function () {
    it("应该防止重入攻击", async function () {
      // 这个测试需要一个恶意合约来模拟重入攻击
      // 由于ReentrancyGuard的存在，重入攻击应该被阻止
      // 这里只是一个基本的测试框架，实际测试需要更复杂的设置
      const depositAmount = ethers.parseEther("100");
      await phrsToken.connect(user1).approve(await depositContract.getAddress(), depositAmount);
      
      // 正常充值应该成功
      await expect(depositContract.connect(user1).deposit(depositAmount))
        .to.emit(depositContract, "Deposit");
    });
  });
});

// 辅助函数
const { anyValue } = require("@nomicfoundation/hardhat-chai-matchers/withArgs");
