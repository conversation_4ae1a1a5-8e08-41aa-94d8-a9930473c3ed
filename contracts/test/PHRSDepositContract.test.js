const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("PHRSDepositContract", function () {
  let phrsDepositContract;
  let owner;
  let user1;
  let user2;
  
  const MIN_DEPOSIT_AMOUNT = ethers.parseEther("1");
  const MAX_DEPOSIT_AMOUNT = ethers.parseEther("10000");

  beforeEach(async function () {
    [owner, user1, user2] = await ethers.getSigners();

    const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");
    phrsDepositContract = await upgrades.deployProxy(
      PHRSDepositContract,
      [MIN_DEPOSIT_AMOUNT, MAX_DEPOSIT_AMOUNT],
      {
        initializer: 'initialize',
        kind: 'transparent'
      }
    );
    await phrsDepositContract.waitForDeployment();
  });

  describe("部署", function () {
    it("应该正确设置初始参数", async function () {
      const contractInfo = await phrsDepositContract.getContractInfo();
      expect(contractInfo[0]).to.equal(MIN_DEPOSIT_AMOUNT);
      expect(contractInfo[1]).to.equal(MAX_DEPOSIT_AMOUNT);
      expect(contractInfo[2]).to.equal(0);
    });

    it("应该设置正确的所有者", async function () {
      expect(await phrsDepositContract.owner()).to.equal(owner.address);
    });
  });

  describe("充值功能", function () {
    it("应该允许有效的充值", async function () {
      const depositAmount = ethers.parseEther("5");
      
      await expect(
        phrsDepositContract.connect(user1).deposit({ value: depositAmount })
      ).to.emit(phrsDepositContract, "Deposit");
    });

    it("应该拒绝零金额充值", async function () {
      await expect(
        phrsDepositContract.connect(user1).deposit({ value: 0 })
      ).to.be.revertedWithCustomError(phrsDepositContract, "InvalidAmount");
    });
  });
});
