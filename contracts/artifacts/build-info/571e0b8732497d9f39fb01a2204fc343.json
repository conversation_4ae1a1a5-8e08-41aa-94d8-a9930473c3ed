{"id": "571e0b8732497d9f39fb01a2204fc343", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.22", "solcLongVersion": "0.8.22+commit.4fc1097e", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {ContextUpgradeable} from \"../utils/ContextUpgradeable.sol\";\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\n    /// @custom:storage-location erc7201:openzeppelin.storage.Ownable\n    struct OwnableStorage {\n        address _owner;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.Ownable\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant OwnableStorageLocation = 0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300;\n\n    function _getOwnableStorage() private pure returns (OwnableStorage storage $) {\n        assembly {\n            $.slot := OwnableStorageLocation\n        }\n    }\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    function __Ownable_init(address initialOwner) internal onlyInitializing {\n        __Ownable_init_unchained(initialOwner);\n    }\n\n    function __Ownable_init_unchained(address initialOwner) internal onlyInitializing {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        OwnableStorage storage $ = _getOwnableStorage();\n        return $._owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        OwnableStorage storage $ = _getOwnableStorage();\n        address oldOwner = $._owner;\n        $._owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (proxy/utils/Initializable.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\n *\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\n * reused. This mechanism prevents re-execution of each \"step\" but allows the creation of new initialization steps in\n * case an upgrade adds a module that needs to be initialized.\n *\n * For example:\n *\n * [.hljs-theme-light.nopadding]\n * ```solidity\n * contract MyToken is ERC20Upgradeable {\n *     function initialize() initializer public {\n *         __ERC20_init(\"MyToken\", \"MTK\");\n *     }\n * }\n *\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\n *     function initializeV2() reinitializer(2) public {\n *         __ERC20Permit_init(\"MyToken\");\n *     }\n * }\n * ```\n *\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\n *\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\n *\n * [CAUTION]\n * ====\n * Avoid leaving a contract uninitialized.\n *\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\n *\n * [.hljs-theme-light.nopadding]\n * ```\n * /// @custom:oz-upgrades-unsafe-allow constructor\n * constructor() {\n *     _disableInitializers();\n * }\n * ```\n * ====\n */\nabstract contract Initializable {\n    /**\n     * @dev Storage of the initializable contract.\n     *\n     * It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\n     * when using with upgradeable contracts.\n     *\n     * @custom:storage-location erc7201:openzeppelin.storage.Initializable\n     */\n    struct InitializableStorage {\n        /**\n         * @dev Indicates that the contract has been initialized.\n         */\n        uint64 _initialized;\n        /**\n         * @dev Indicates that the contract is in the process of being initialized.\n         */\n        bool _initializing;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.Initializable\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant INITIALIZABLE_STORAGE = 0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00;\n\n    /**\n     * @dev The contract is already initialized.\n     */\n    error InvalidInitialization();\n\n    /**\n     * @dev The contract is not initializing.\n     */\n    error NotInitializing();\n\n    /**\n     * @dev Triggered when the contract has been initialized or reinitialized.\n     */\n    event Initialized(uint64 version);\n\n    /**\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\n     * `onlyInitializing` functions can be used to initialize parent contracts.\n     *\n     * Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\n     * number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\n     * production.\n     *\n     * Emits an {Initialized} event.\n     */\n    modifier initializer() {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        // Cache values to avoid duplicated sloads\n        bool isTopLevelCall = !$._initializing;\n        uint64 initialized = $._initialized;\n\n        // Allowed calls:\n        // - initialSetup: the contract is not in the initializing state and no previous version was\n        //                 initialized\n        // - construction: the contract is initialized at version 1 (no reinitialization) and the\n        //                 current contract is just being deployed\n        bool initialSetup = initialized == 0 && isTopLevelCall;\n        bool construction = initialized == 1 && address(this).code.length == 0;\n\n        if (!initialSetup && !construction) {\n            revert InvalidInitialization();\n        }\n        $._initialized = 1;\n        if (isTopLevelCall) {\n            $._initializing = true;\n        }\n        _;\n        if (isTopLevelCall) {\n            $._initializing = false;\n            emit Initialized(1);\n        }\n    }\n\n    /**\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\n     * used to initialize parent contracts.\n     *\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\n     * are added through upgrades and that require initialization.\n     *\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\n     *\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\n     * a contract, executing them in the right order is up to the developer or operator.\n     *\n     * WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\n     *\n     * Emits an {Initialized} event.\n     */\n    modifier reinitializer(uint64 version) {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        if ($._initializing || $._initialized >= version) {\n            revert InvalidInitialization();\n        }\n        $._initialized = version;\n        $._initializing = true;\n        _;\n        $._initializing = false;\n        emit Initialized(version);\n    }\n\n    /**\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\n     */\n    modifier onlyInitializing() {\n        _checkInitializing();\n        _;\n    }\n\n    /**\n     * @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}.\n     */\n    function _checkInitializing() internal view virtual {\n        if (!_isInitializing()) {\n            revert NotInitializing();\n        }\n    }\n\n    /**\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\n     * through proxies.\n     *\n     * Emits an {Initialized} event the first time it is successfully executed.\n     */\n    function _disableInitializers() internal virtual {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        if ($._initializing) {\n            revert InvalidInitialization();\n        }\n        if ($._initialized != type(uint64).max) {\n            $._initialized = type(uint64).max;\n            emit Initialized(type(uint64).max);\n        }\n    }\n\n    /**\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\n     */\n    function _getInitializedVersion() internal view returns (uint64) {\n        return _getInitializableStorage()._initialized;\n    }\n\n    /**\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\n     */\n    function _isInitializing() internal view returns (bool) {\n        return _getInitializableStorage()._initializing;\n    }\n\n    /**\n     * @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\n     *\n     * NOTE: Consider following the ERC-7201 formula to derive storage locations.\n     */\n    function _initializableStorageSlot() internal pure virtual returns (bytes32) {\n        return INITIALIZABLE_STORAGE;\n    }\n\n    /**\n     * @dev Returns a pointer to the storage namespace.\n     */\n    // solhint-disable-next-line var-name-mixedcase\n    function _getInitializableStorage() private pure returns (InitializableStorage storage $) {\n        bytes32 slot = _initializableStorageSlot();\n        assembly {\n            $.slot := slot\n        }\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract ContextUpgradeable is Initializable {\n    function __Context_init() internal onlyInitializing {\n    }\n\n    function __Context_init_unchained() internal onlyInitializing {\n    }\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (utils/Pausable.sol)\n\npragma solidity ^0.8.20;\n\nimport {ContextUpgradeable} from \"../utils/ContextUpgradeable.sol\";\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Contract module which allows children to implement an emergency stop\n * mechanism that can be triggered by an authorized account.\n *\n * This module is used through inheritance. It will make available the\n * modifiers `whenNotPaused` and `whenPaused`, which can be applied to\n * the functions of your contract. Note that they will not be pausable by\n * simply including this module, only once the modifiers are put in place.\n */\nabstract contract PausableUpgradeable is Initializable, ContextUpgradeable {\n    /// @custom:storage-location erc7201:openzeppelin.storage.Pausable\n    struct PausableStorage {\n        bool _paused;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.Pausable\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant PausableStorageLocation = 0xcd5ed15c6e187e77e9aee88184c21f4f2182ab5827cb3b7e07fbedcd63f03300;\n\n    function _getPausableStorage() private pure returns (PausableStorage storage $) {\n        assembly {\n            $.slot := PausableStorageLocation\n        }\n    }\n\n    /**\n     * @dev Emitted when the pause is triggered by `account`.\n     */\n    event Paused(address account);\n\n    /**\n     * @dev Emitted when the pause is lifted by `account`.\n     */\n    event Unpaused(address account);\n\n    /**\n     * @dev The operation failed because the contract is paused.\n     */\n    error EnforcedPause();\n\n    /**\n     * @dev The operation failed because the contract is not paused.\n     */\n    error ExpectedPause();\n\n    /**\n     * @dev Modifier to make a function callable only when the contract is not paused.\n     *\n     * Requirements:\n     *\n     * - The contract must not be paused.\n     */\n    modifier whenNotPaused() {\n        _requireNotPaused();\n        _;\n    }\n\n    /**\n     * @dev Modifier to make a function callable only when the contract is paused.\n     *\n     * Requirements:\n     *\n     * - The contract must be paused.\n     */\n    modifier whenPaused() {\n        _requirePaused();\n        _;\n    }\n\n    function __Pausable_init() internal onlyInitializing {\n    }\n\n    function __Pausable_init_unchained() internal onlyInitializing {\n    }\n    /**\n     * @dev Returns true if the contract is paused, and false otherwise.\n     */\n    function paused() public view virtual returns (bool) {\n        PausableStorage storage $ = _getPausableStorage();\n        return $._paused;\n    }\n\n    /**\n     * @dev Throws if the contract is paused.\n     */\n    function _requireNotPaused() internal view virtual {\n        if (paused()) {\n            revert EnforcedPause();\n        }\n    }\n\n    /**\n     * @dev Throws if the contract is not paused.\n     */\n    function _requirePaused() internal view virtual {\n        if (!paused()) {\n            revert ExpectedPause();\n        }\n    }\n\n    /**\n     * @dev Triggers stopped state.\n     *\n     * Requirements:\n     *\n     * - The contract must not be paused.\n     */\n    function _pause() internal virtual whenNotPaused {\n        PausableStorage storage $ = _getPausableStorage();\n        $._paused = true;\n        emit Paused(_msgSender());\n    }\n\n    /**\n     * @dev Returns to normal state.\n     *\n     * Requirements:\n     *\n     * - The contract must be paused.\n     */\n    function _unpause() internal virtual whenPaused {\n        PausableStorage storage $ = _getPausableStorage();\n        $._paused = false;\n        emit Unpaused(_msgSender());\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\n\npragma solidity ^0.8.20;\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Contract module that helps prevent reentrant calls to a function.\n *\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n * available, which can be applied to functions to make sure there are no nested\n * (reentrant) calls to them.\n *\n * Note that because there is a single `nonReentrant` guard, functions marked as\n * `nonReentrant` may not call one another. This can be worked around by making\n * those functions `private`, and then adding `external` `nonReentrant` entry\n * points to them.\n *\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n * consider using {ReentrancyGuardTransient} instead.\n *\n * TIP: If you would like to learn more about reentrancy and alternative ways\n * to protect against it, check out our blog post\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\n */\nabstract contract ReentrancyGuardUpgradeable is Initializable {\n    // Booleans are more expensive than uint256 or any type that takes up a full\n    // word because each write operation emits an extra SLOAD to first read the\n    // slot's contents, replace the bits taken up by the boolean, and then write\n    // back. This is the compiler's defense against contract upgrades and\n    // pointer aliasing, and it cannot be disabled.\n\n    // The values being non-zero value makes deployment a bit more expensive,\n    // but in exchange the refund on every call to nonReentrant will be lower in\n    // amount. Since refunds are capped to a percentage of the total\n    // transaction's gas, it is best to keep them low in cases like this one, to\n    // increase the likelihood of the full refund coming into effect.\n    uint256 private constant NOT_ENTERED = 1;\n    uint256 private constant ENTERED = 2;\n\n    /// @custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard\n    struct ReentrancyGuardStorage {\n        uint256 _status;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.ReentrancyGuard\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant ReentrancyGuardStorageLocation = 0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00;\n\n    function _getReentrancyGuardStorage() private pure returns (ReentrancyGuardStorage storage $) {\n        assembly {\n            $.slot := ReentrancyGuardStorageLocation\n        }\n    }\n\n    /**\n     * @dev Unauthorized reentrant call.\n     */\n    error ReentrancyGuardReentrantCall();\n\n    function __ReentrancyGuard_init() internal onlyInitializing {\n        __ReentrancyGuard_init_unchained();\n    }\n\n    function __ReentrancyGuard_init_unchained() internal onlyInitializing {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        $._status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Prevents a contract from calling itself, directly or indirectly.\n     * Calling a `nonReentrant` function from another `nonReentrant`\n     * function is not supported. It is possible to prevent this from happening\n     * by making the `nonReentrant` function external, and making it call a\n     * `private` function that does the actual work.\n     */\n    modifier nonReentrant() {\n        _nonReentrantBefore();\n        _;\n        _nonReentrantAfter();\n    }\n\n    function _nonReentrantBefore() private {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\n        if ($._status == ENTERED) {\n            revert ReentrancyGuardReentrantCall();\n        }\n\n        // Any calls to nonReentrant after this point will fail\n        $._status = ENTERED;\n    }\n\n    function _nonReentrantAfter() private {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        // By storing the original value once again, a refund is triggered (see\n        // https://eips.ethereum.org/EIPS/eip-2200)\n        $._status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n     * `nonReentrant` function in the call stack.\n     */\n    function _reentrancyGuardEntered() internal view returns (bool) {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        return $._status == ENTERED;\n    }\n}\n"}, "contracts/PHRSDepositContract.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.22;\n\nimport \"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\";\n\n/**\n * @title PHRS Deposit Contract\n * @dev 用于处理PHRS原生币充值到游戏系统的可升级智能合约\n * @notice 支持用户将PHRS原生币充值到游戏账户，发射事件供后端监听\n */\ncontract PHRSDepositContract is\n    Initializable,\n    ReentrancyGuardUpgradeable,\n    OwnableUpgradeable,\n    PausableUpgradeable\n{\n    // 最小充值金额\n    uint256 public minDepositAmount;\n\n    // 最大充值金额\n    uint256 public maxDepositAmount;\n\n    // 事件定义\n    event Deposit(\n        address indexed user,\n        uint256 amount,\n        uint256 timestamp\n    );\n\n    event MinDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);\n    event MaxDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);\n    event EmergencyWithdraw(uint256 amount, address indexed to);\n\n    // 错误定义\n    error InvalidAmount();\n    error AmountTooSmall();\n    error AmountTooLarge();\n    error TransferFailed();\n    error InvalidAddress();\n\n    /// @custom:oz-upgrades-unsafe-allow constructor\n    constructor() {\n        _disableInitializers();\n    }\n\n    /**\n     * @dev 初始化函数，替代构造函数\n     * @param _minDepositAmount 最小充值金额\n     * @param _maxDepositAmount 最大充值金额\n     */\n    function initialize(\n        uint256 _minDepositAmount,\n        uint256 _maxDepositAmount\n    ) public initializer {\n        if (_minDepositAmount == 0) revert InvalidAmount();\n        if (_maxDepositAmount <= _minDepositAmount) revert InvalidAmount();\n\n        __ReentrancyGuard_init();\n        __Ownable_init(msg.sender);\n        __Pausable_init();\n\n        minDepositAmount = _minDepositAmount;\n        maxDepositAmount = _maxDepositAmount;\n    }\n\n    /**\n     * @dev 充值PHRS原生币\n     * @notice 用户发送PHRS原生币到此函数进行充值\n     */\n    function deposit() external payable nonReentrant whenNotPaused {\n        _processDeposit();\n    }\n\n    /**\n     * @dev 接收原生币的回退函数\n     */\n    receive() external payable {\n        _processDeposit();\n    }\n\n    /**\n     * @dev 内部充值处理函数\n     */\n    function _processDeposit() internal {\n        uint256 amount = msg.value;\n\n        if (amount == 0) revert InvalidAmount();\n        if (amount < minDepositAmount) revert AmountTooSmall();\n        if (amount > maxDepositAmount) revert AmountTooLarge();\n\n        address user = msg.sender;\n\n        // 发射充值事件供后端监听\n        emit Deposit(user, amount, block.timestamp);\n    }\n\n\n\n    /**\n     * @dev 设置最小充值金额（仅管理员）\n     * @param _minDepositAmount 新的最小充值金额\n     */\n    function setMinDepositAmount(uint256 _minDepositAmount) external onlyOwner {\n        require(_minDepositAmount > 0, \"Amount must be greater than 0\");\n        require(_minDepositAmount < maxDepositAmount, \"Min amount must be less than max amount\");\n        \n        uint256 oldAmount = minDepositAmount;\n        minDepositAmount = _minDepositAmount;\n        \n        emit MinDepositAmountUpdated(oldAmount, _minDepositAmount);\n    }\n\n    /**\n     * @dev 设置最大充值金额（仅管理员）\n     * @param _maxDepositAmount 新的最大充值金额\n     */\n    function setMaxDepositAmount(uint256 _maxDepositAmount) external onlyOwner {\n        require(_maxDepositAmount > minDepositAmount, \"Max amount must be greater than min amount\");\n        \n        uint256 oldAmount = maxDepositAmount;\n        maxDepositAmount = _maxDepositAmount;\n        \n        emit MaxDepositAmountUpdated(oldAmount, _maxDepositAmount);\n    }\n\n    /**\n     * @dev 暂停合约（仅管理员）\n     */\n    function pause() external onlyOwner {\n        _pause();\n    }\n\n    /**\n     * @dev 恢复合约（仅管理员）\n     */\n    function unpause() external onlyOwner {\n        _unpause();\n    }\n\n    /**\n     * @dev 紧急提取原生币（仅管理员）\n     * @param amount 提取金额\n     * @param to 接收地址\n     */\n    function emergencyWithdraw(\n        uint256 amount,\n        address payable to\n    ) external onlyOwner {\n        if (to == address(0)) revert InvalidAddress();\n        if (amount > address(this).balance) revert InvalidAmount();\n\n        (bool success, ) = to.call{value: amount}(\"\");\n        if (!success) revert TransferFailed();\n\n        emit EmergencyWithdraw(amount, to);\n    }\n\n    /**\n     * @dev 获取合约原生币余额\n     * @return 合约原生币余额\n     */\n    function getBalance() external view returns (uint256) {\n        return address(this).balance;\n    }\n\n    /**\n     * @dev 获取合约基本信息\n     * @return minAmount 最小充值金额\n     * @return maxAmount 最大充值金额\n     * @return contractBalance 合约原生币余额\n     */\n    function getContractInfo() external view returns (\n        uint256 minAmount,\n        uint256 maxAmount,\n        uint256 contractBalance\n    ) {\n        return (\n            minDepositAmount,\n            maxDepositAmount,\n            address(this).balance\n        );\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "output": {"sources": {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "exportedSymbols": {"ContextUpgradeable": [508], "Initializable": [462], "OwnableUpgradeable": [194]}, "id": 195, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:0"}, {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "file": "../utils/ContextUpgradeable.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 195, "sourceUnit": 509, "src": "128:67:0", "symbolAliases": [{"foreign": {"id": 2, "name": "ContextUpgradeable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 508, "src": "136:18:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "file": "../proxy/utils/Initializable.sol", "id": 5, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 195, "sourceUnit": 463, "src": "196:63:0", "symbolAliases": [{"foreign": {"id": 4, "name": "Initializable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "204:13:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 7, "name": "Initializable", "nameLocations": ["789:13:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 462, "src": "789:13:0"}, "id": 8, "nodeType": "InheritanceSpecifier", "src": "789:13:0"}, {"baseName": {"id": 9, "name": "ContextUpgradeable", "nameLocations": ["804:18:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 508, "src": "804:18:0"}, "id": 10, "nodeType": "InheritanceSpecifier", "src": "804:18:0"}], "canonicalName": "OwnableUpgradeable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 6, "nodeType": "StructuredDocumentation", "src": "261:487:0", "text": " @dev Contract module which provides a basic access control mechanism, where\n there is an account (an owner) that can be granted exclusive access to\n specific functions.\n The initial owner is set to the address provided by the deployer. This can\n later be changed with {transferOwnership}.\n This module is used through inheritance. It will make available the modifier\n `onlyOwner`, which can be applied to your functions to restrict their use to\n the owner."}, "fullyImplemented": true, "id": 194, "linearizedBaseContracts": [194, 508, 462], "name": "OwnableUpgradeable", "nameLocation": "767:18:0", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "OwnableUpgradeable.OwnableStorage", "documentation": {"id": 11, "nodeType": "StructuredDocumentation", "src": "829:65:0", "text": "@custom:storage-location erc7201:openzeppelin.storage.Ownable"}, "id": 14, "members": [{"constant": false, "id": 13, "mutability": "mutable", "name": "_owner", "nameLocation": "939:6:0", "nodeType": "VariableDeclaration", "scope": 14, "src": "931:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 12, "name": "address", "nodeType": "ElementaryTypeName", "src": "931:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "name": "OwnableStorage", "nameLocation": "906:14:0", "nodeType": "StructDefinition", "scope": 194, "src": "899:53:0", "visibility": "public"}, {"constant": true, "id": 17, "mutability": "constant", "name": "OwnableStorageLocation", "nameLocation": "1094:22:0", "nodeType": "VariableDeclaration", "scope": 194, "src": "1069:116:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 15, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1069:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"hexValue": "307839303136643039643732643430666461653266643863656163366236323334633737303632313466643339633163643165363039613035323863313939333030", "id": 16, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1119:66:0", "typeDescriptions": {"typeIdentifier": "t_rational_65173360639460082030725920392146925864023520599682862633725751242436743107328_by_1", "typeString": "int_const 6517...(69 digits omitted)...7328"}, "value": "0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300"}, "visibility": "private"}, {"body": {"id": 24, "nodeType": "Block", "src": "1270:81:0", "statements": [{"AST": {"nativeSrc": "1289:56:0", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1289:56:0", "statements": [{"nativeSrc": "1303:32:0", "nodeType": "YulAssignment", "src": "1303:32:0", "value": {"name": "OwnableStorageLocation", "nativeSrc": "1313:22:0", "nodeType": "YulIdentifier", "src": "1313:22:0"}, "variableNames": [{"name": "$.slot", "nativeSrc": "1303:6:0", "nodeType": "YulIdentifier", "src": "1303:6:0"}]}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 21, "isOffset": false, "isSlot": true, "src": "1303:6:0", "suffix": "slot", "valueSize": 1}, {"declaration": 17, "isOffset": false, "isSlot": false, "src": "1313:22:0", "valueSize": 1}], "id": 23, "nodeType": "InlineAssembly", "src": "1280:65:0"}]}, "id": 25, "implemented": true, "kind": "function", "modifiers": [], "name": "_getOwnableStorage", "nameLocation": "1201:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 18, "nodeType": "ParameterList", "parameters": [], "src": "1219:2:0"}, "returnParameters": {"id": 22, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 21, "mutability": "mutable", "name": "$", "nameLocation": "1267:1:0", "nodeType": "VariableDeclaration", "scope": 25, "src": "1244:24:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}, "typeName": {"id": 20, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 19, "name": "OwnableStorage", "nameLocations": ["1244:14:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 14, "src": "1244:14:0"}, "referencedDeclaration": 14, "src": "1244:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}}, "visibility": "internal"}], "src": "1243:26:0"}, "scope": 194, "src": "1192:159:0", "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"documentation": {"id": 26, "nodeType": "StructuredDocumentation", "src": "1357:85:0", "text": " @dev The caller account is not authorized to perform an operation."}, "errorSelector": "118cdaa7", "id": 30, "name": "OwnableUnauthorizedAccount", "nameLocation": "1453:26:0", "nodeType": "ErrorDefinition", "parameters": {"id": 29, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 28, "mutability": "mutable", "name": "account", "nameLocation": "1488:7:0", "nodeType": "VariableDeclaration", "scope": 30, "src": "1480:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 27, "name": "address", "nodeType": "ElementaryTypeName", "src": "1480:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1479:17:0"}, "src": "1447:50:0"}, {"documentation": {"id": 31, "nodeType": "StructuredDocumentation", "src": "1503:82:0", "text": " @dev The owner is not a valid owner account. (eg. `address(0)`)"}, "errorSelector": "1e4fbdf7", "id": 35, "name": "OwnableInvalidOwner", "nameLocation": "1596:19:0", "nodeType": "ErrorDefinition", "parameters": {"id": 34, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 33, "mutability": "mutable", "name": "owner", "nameLocation": "1624:5:0", "nodeType": "VariableDeclaration", "scope": 35, "src": "1616:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 32, "name": "address", "nodeType": "ElementaryTypeName", "src": "1616:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1615:15:0"}, "src": "1590:41:0"}, {"anonymous": false, "eventSelector": "8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "id": 41, "name": "OwnershipTransferred", "nameLocation": "1643:20:0", "nodeType": "EventDefinition", "parameters": {"id": 40, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 37, "indexed": true, "mutability": "mutable", "name": "previousOwner", "nameLocation": "1680:13:0", "nodeType": "VariableDeclaration", "scope": 41, "src": "1664:29:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 36, "name": "address", "nodeType": "ElementaryTypeName", "src": "1664:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 39, "indexed": true, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "1711:8:0", "nodeType": "VariableDeclaration", "scope": 41, "src": "1695:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 38, "name": "address", "nodeType": "ElementaryTypeName", "src": "1695:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1663:57:0"}, "src": "1637:84:0"}, {"body": {"id": 53, "nodeType": "Block", "src": "1919:55:0", "statements": [{"expression": {"arguments": [{"id": 50, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 44, "src": "1954:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 49, "name": "__Ownable_init_unchained", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "1929:24:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 51, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1929:38:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 52, "nodeType": "ExpressionStatement", "src": "1929:38:0"}]}, "documentation": {"id": 42, "nodeType": "StructuredDocumentation", "src": "1727:115:0", "text": " @dev Initializes the contract setting the address provided by the deployer as the initial owner."}, "id": 54, "implemented": true, "kind": "function", "modifiers": [{"id": 47, "kind": "modifierInvocation", "modifierName": {"id": 46, "name": "onlyInitializing", "nameLocations": ["1902:16:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "1902:16:0"}, "nodeType": "ModifierInvocation", "src": "1902:16:0"}], "name": "__Ownable_init", "nameLocation": "1856:14:0", "nodeType": "FunctionDefinition", "parameters": {"id": 45, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 44, "mutability": "mutable", "name": "initialOwner", "nameLocation": "1879:12:0", "nodeType": "VariableDeclaration", "scope": 54, "src": "1871:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 43, "name": "address", "nodeType": "ElementaryTypeName", "src": "1871:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1870:22:0"}, "returnParameters": {"id": 48, "nodeType": "ParameterList", "parameters": [], "src": "1919:0:0"}, "scope": 194, "src": "1847:127:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 80, "nodeType": "Block", "src": "2062:153:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 66, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 61, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 56, "src": "2076:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 64, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2100:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 63, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2092:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 62, "name": "address", "nodeType": "ElementaryTypeName", "src": "2092:7:0", "typeDescriptions": {}}}, "id": 65, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2092:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2076:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 75, "nodeType": "IfStatement", "src": "2072:95:0", "trueBody": {"id": 74, "nodeType": "Block", "src": "2104:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 70, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2153:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 69, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2145:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 68, "name": "address", "nodeType": "ElementaryTypeName", "src": "2145:7:0", "typeDescriptions": {}}}, "id": 71, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2145:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 67, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 35, "src": "2125:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2125:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 73, "nodeType": "RevertStatement", "src": "2118:38:0"}]}}, {"expression": {"arguments": [{"id": 77, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 56, "src": "2195:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 76, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "2176:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2176:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 79, "nodeType": "ExpressionStatement", "src": "2176:32:0"}]}, "id": 81, "implemented": true, "kind": "function", "modifiers": [{"id": 59, "kind": "modifierInvocation", "modifierName": {"id": 58, "name": "onlyInitializing", "nameLocations": ["2045:16:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "2045:16:0"}, "nodeType": "ModifierInvocation", "src": "2045:16:0"}], "name": "__Ownable_init_unchained", "nameLocation": "1989:24:0", "nodeType": "FunctionDefinition", "parameters": {"id": 57, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 56, "mutability": "mutable", "name": "initialOwner", "nameLocation": "2022:12:0", "nodeType": "VariableDeclaration", "scope": 81, "src": "2014:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 55, "name": "address", "nodeType": "ElementaryTypeName", "src": "2014:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2013:22:0"}, "returnParameters": {"id": 60, "nodeType": "ParameterList", "parameters": [], "src": "2062:0:0"}, "scope": 194, "src": "1980:235:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 88, "nodeType": "Block", "src": "2324:41:0", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 84, "name": "_checkOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 122, "src": "2334:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 85, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2334:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 86, "nodeType": "ExpressionStatement", "src": "2334:13:0"}, {"id": 87, "nodeType": "PlaceholderStatement", "src": "2357:1:0"}]}, "documentation": {"id": 82, "nodeType": "StructuredDocumentation", "src": "2221:77:0", "text": " @dev Throws if called by any account other than the owner."}, "id": 89, "name": "only<PERSON><PERSON>er", "nameLocation": "2312:9:0", "nodeType": "ModifierDefinition", "parameters": {"id": 83, "nodeType": "ParameterList", "parameters": [], "src": "2321:2:0"}, "src": "2303:62:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 104, "nodeType": "Block", "src": "2496:89:0", "statements": [{"assignments": [97], "declarations": [{"constant": false, "id": 97, "mutability": "mutable", "name": "$", "nameLocation": "2529:1:0", "nodeType": "VariableDeclaration", "scope": 104, "src": "2506:24:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}, "typeName": {"id": 96, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 95, "name": "OwnableStorage", "nameLocations": ["2506:14:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 14, "src": "2506:14:0"}, "referencedDeclaration": 14, "src": "2506:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}}, "visibility": "internal"}], "id": 100, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 98, "name": "_getOwnableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "2533:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_OwnableStorage_$14_storage_ptr_$", "typeString": "function () pure returns (struct OwnableUpgradeable.OwnableStorage storage pointer)"}}, "id": 99, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2533:20:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "2506:47:0"}, {"expression": {"expression": {"id": 101, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 97, "src": "2570:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage storage pointer"}}, "id": 102, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2572:6:0", "memberName": "_owner", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "2570:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 94, "id": 103, "nodeType": "Return", "src": "2563:15:0"}]}, "documentation": {"id": 90, "nodeType": "StructuredDocumentation", "src": "2371:65:0", "text": " @dev Returns the address of the current owner."}, "functionSelector": "8da5cb5b", "id": 105, "implemented": true, "kind": "function", "modifiers": [], "name": "owner", "nameLocation": "2450:5:0", "nodeType": "FunctionDefinition", "parameters": {"id": 91, "nodeType": "ParameterList", "parameters": [], "src": "2455:2:0"}, "returnParameters": {"id": 94, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 93, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 105, "src": "2487:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 92, "name": "address", "nodeType": "ElementaryTypeName", "src": "2487:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2486:9:0"}, "scope": 194, "src": "2441:144:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 121, "nodeType": "Block", "src": "2703:117:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 113, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 109, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "2717:5:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2717:7:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 111, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 490, "src": "2728:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2728:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2717:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 120, "nodeType": "IfStatement", "src": "2713:101:0", "trueBody": {"id": 119, "nodeType": "Block", "src": "2742:72:0", "statements": [{"errorCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 115, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 490, "src": "2790:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 116, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2790:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 114, "name": "OwnableUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 30, "src": "2763:26:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2763:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 118, "nodeType": "RevertStatement", "src": "2756:47:0"}]}}]}, "documentation": {"id": 106, "nodeType": "StructuredDocumentation", "src": "2591:62:0", "text": " @dev Throws if the sender is not the owner."}, "id": 122, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkOwner", "nameLocation": "2667:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 107, "nodeType": "ParameterList", "parameters": [], "src": "2678:2:0"}, "returnParameters": {"id": 108, "nodeType": "ParameterList", "parameters": [], "src": "2703:0:0"}, "scope": 194, "src": "2658:162:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 135, "nodeType": "Block", "src": "3209:47:0", "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 131, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3246:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 130, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3238:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 129, "name": "address", "nodeType": "ElementaryTypeName", "src": "3238:7:0", "typeDescriptions": {}}}, "id": 132, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3238:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 128, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "3219:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 133, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3219:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 134, "nodeType": "ExpressionStatement", "src": "3219:30:0"}]}, "documentation": {"id": 123, "nodeType": "StructuredDocumentation", "src": "2826:324:0", "text": " @dev Leaves the contract without owner. It will not be possible to call\n `onlyOwner` functions. Can only be called by the current owner.\n NOTE: Renouncing ownership will leave the contract without an owner,\n thereby disabling any functionality that is only available to the owner."}, "functionSelector": "715018a6", "id": 136, "implemented": true, "kind": "function", "modifiers": [{"id": 126, "kind": "modifierInvocation", "modifierName": {"id": 125, "name": "only<PERSON><PERSON>er", "nameLocations": ["3199:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "3199:9:0"}, "nodeType": "ModifierInvocation", "src": "3199:9:0"}], "name": "renounceOwnership", "nameLocation": "3164:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 124, "nodeType": "ParameterList", "parameters": [], "src": "3181:2:0"}, "returnParameters": {"id": 127, "nodeType": "ParameterList", "parameters": [], "src": "3209:0:0"}, "scope": 194, "src": "3155:101:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 163, "nodeType": "Block", "src": "3475:145:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 149, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 144, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 139, "src": "3489:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 147, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3509:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 146, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3501:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 145, "name": "address", "nodeType": "ElementaryTypeName", "src": "3501:7:0", "typeDescriptions": {}}}, "id": 148, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3501:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3489:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 158, "nodeType": "IfStatement", "src": "3485:91:0", "trueBody": {"id": 157, "nodeType": "Block", "src": "3513:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 153, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3562:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 152, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3554:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 151, "name": "address", "nodeType": "ElementaryTypeName", "src": "3554:7:0", "typeDescriptions": {}}}, "id": 154, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3554:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 150, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 35, "src": "3534:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 155, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3534:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 156, "nodeType": "RevertStatement", "src": "3527:38:0"}]}}, {"expression": {"arguments": [{"id": 160, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 139, "src": "3604:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 159, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "3585:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 161, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3585:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 162, "nodeType": "ExpressionStatement", "src": "3585:28:0"}]}, "documentation": {"id": 137, "nodeType": "StructuredDocumentation", "src": "3262:138:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Can only be called by the current owner."}, "functionSelector": "f2fde38b", "id": 164, "implemented": true, "kind": "function", "modifiers": [{"id": 142, "kind": "modifierInvocation", "modifierName": {"id": 141, "name": "only<PERSON><PERSON>er", "nameLocations": ["3465:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "3465:9:0"}, "nodeType": "ModifierInvocation", "src": "3465:9:0"}], "name": "transferOwnership", "nameLocation": "3414:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 140, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 139, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "3440:8:0", "nodeType": "VariableDeclaration", "scope": 164, "src": "3432:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 138, "name": "address", "nodeType": "ElementaryTypeName", "src": "3432:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3431:18:0"}, "returnParameters": {"id": 143, "nodeType": "ParameterList", "parameters": [], "src": "3475:0:0"}, "scope": 194, "src": "3405:215:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 192, "nodeType": "Block", "src": "3837:185:0", "statements": [{"assignments": [172], "declarations": [{"constant": false, "id": 172, "mutability": "mutable", "name": "$", "nameLocation": "3870:1:0", "nodeType": "VariableDeclaration", "scope": 192, "src": "3847:24:0", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}, "typeName": {"id": 171, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 170, "name": "OwnableStorage", "nameLocations": ["3847:14:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 14, "src": "3847:14:0"}, "referencedDeclaration": 14, "src": "3847:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage"}}, "visibility": "internal"}], "id": 175, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 173, "name": "_getOwnableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "3874:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_OwnableStorage_$14_storage_ptr_$", "typeString": "function () pure returns (struct OwnableUpgradeable.OwnableStorage storage pointer)"}}, "id": 174, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3874:20:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "3847:47:0"}, {"assignments": [177], "declarations": [{"constant": false, "id": 177, "mutability": "mutable", "name": "old<PERSON>wner", "nameLocation": "3912:8:0", "nodeType": "VariableDeclaration", "scope": 192, "src": "3904:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 176, "name": "address", "nodeType": "ElementaryTypeName", "src": "3904:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 180, "initialValue": {"expression": {"id": 178, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "3923:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage storage pointer"}}, "id": 179, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3925:6:0", "memberName": "_owner", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "3923:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "3904:27:0"}, {"expression": {"id": 185, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 181, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "3941:1:0", "typeDescriptions": {"typeIdentifier": "t_struct$_OwnableStorage_$14_storage_ptr", "typeString": "struct OwnableUpgradeable.OwnableStorage storage pointer"}}, "id": 183, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "3943:6:0", "memberName": "_owner", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "3941:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 184, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 167, "src": "3952:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3941:19:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 186, "nodeType": "ExpressionStatement", "src": "3941:19:0"}, {"eventCall": {"arguments": [{"id": 188, "name": "old<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 177, "src": "3996:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 189, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 167, "src": "4006:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 187, "name": "OwnershipTransferred", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41, "src": "3975:20:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$returns$__$", "typeString": "function (address,address)"}}, "id": 190, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3975:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 191, "nodeType": "EmitStatement", "src": "3970:45:0"}]}, "documentation": {"id": 165, "nodeType": "StructuredDocumentation", "src": "3626:143:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Internal function without access restriction."}, "id": 193, "implemented": true, "kind": "function", "modifiers": [], "name": "_transferOwnership", "nameLocation": "3783:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 168, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 167, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "3810:8:0", "nodeType": "VariableDeclaration", "scope": 193, "src": "3802:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 166, "name": "address", "nodeType": "ElementaryTypeName", "src": "3802:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3801:18:0"}, "returnParameters": {"id": 169, "nodeType": "ParameterList", "parameters": [], "src": "3837:0:0"}, "scope": 194, "src": "3774:248:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 195, "src": "749:3275:0", "usedErrors": [30, 35, 211, 214], "usedEvents": [41, 219]}], "src": "102:3923:0"}, "id": 0}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "exportedSymbols": {"Initializable": [462]}, "id": 463, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 196, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "113:24:1"}, {"abstract": true, "baseContracts": [], "canonicalName": "Initializable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 197, "nodeType": "StructuredDocumentation", "src": "139:2209:1", "text": " @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\n behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\n external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\n function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\n The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\n reused. This mechanism prevents re-execution of each \"step\" but allows the creation of new initialization steps in\n case an upgrade adds a module that needs to be initialized.\n For example:\n [.hljs-theme-light.nopadding]\n ```solidity\n contract MyToken is ERC20Upgradeable {\n     function initialize() initializer public {\n         __ERC20_init(\"MyToken\", \"MTK\");\n     }\n }\n contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\n     function initializeV2() reinitializer(2) public {\n         __ERC20Permit_init(\"MyToken\");\n     }\n }\n ```\n TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\n possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\n CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\n that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\n [CAUTION]\n ====\n Avoid leaving a contract uninitialized.\n An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\n contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\n the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\n [.hljs-theme-light.nopadding]\n ```\n /// @custom:oz-upgrades-unsafe-allow constructor\n constructor() {\n     _disableInitializers();\n }\n ```\n ===="}, "fullyImplemented": true, "id": 462, "linearizedBaseContracts": [462], "name": "Initializable", "nameLocation": "2367:13:1", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "Initializable.InitializableStorage", "documentation": {"id": 198, "nodeType": "StructuredDocumentation", "src": "2387:293:1", "text": " @dev Storage of the initializable contract.\n It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\n when using with upgradeable contracts.\n @custom:storage-location erc7201:openzeppelin.storage.Initializable"}, "id": 205, "members": [{"constant": false, "id": 201, "mutability": "mutable", "name": "_initialized", "nameLocation": "2820:12:1", "nodeType": "VariableDeclaration", "scope": 205, "src": "2813:19:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 200, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "2813:6:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}, {"constant": false, "id": 204, "mutability": "mutable", "name": "_initializing", "nameLocation": "2955:13:1", "nodeType": "VariableDeclaration", "scope": 205, "src": "2950:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 203, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2950:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "InitializableStorage", "nameLocation": "2692:20:1", "nodeType": "StructDefinition", "scope": 462, "src": "2685:290:1", "visibility": "public"}, {"constant": true, "id": 208, "mutability": "constant", "name": "INITIALIZABLE_STORAGE", "nameLocation": "3123:21:1", "nodeType": "VariableDeclaration", "scope": 462, "src": "3098:115:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 206, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3098:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"hexValue": "307866306335376531363834306466303430663135303838646332663831666533393163333932336265633733653233613936363265666339633232396336613030", "id": 207, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3147:66:1", "typeDescriptions": {"typeIdentifier": "t_rational_108904022758810753673719992590105913556127789646572562039383141376366747609600_by_1", "typeString": "int_const 1089...(70 digits omitted)...9600"}, "value": "0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00"}, "visibility": "private"}, {"documentation": {"id": 209, "nodeType": "StructuredDocumentation", "src": "3220:60:1", "text": " @dev The contract is already initialized."}, "errorSelector": "f92ee8a9", "id": 211, "name": "InvalidInitialization", "nameLocation": "3291:21:1", "nodeType": "ErrorDefinition", "parameters": {"id": 210, "nodeType": "ParameterList", "parameters": [], "src": "3312:2:1"}, "src": "3285:30:1"}, {"documentation": {"id": 212, "nodeType": "StructuredDocumentation", "src": "3321:57:1", "text": " @dev The contract is not initializing."}, "errorSelector": "d7e6bcf8", "id": 214, "name": "NotInitializing", "nameLocation": "3389:15:1", "nodeType": "ErrorDefinition", "parameters": {"id": 213, "nodeType": "ParameterList", "parameters": [], "src": "3404:2:1"}, "src": "3383:24:1"}, {"anonymous": false, "documentation": {"id": 215, "nodeType": "StructuredDocumentation", "src": "3413:90:1", "text": " @dev Triggered when the contract has been initialized or reinitialized."}, "eventSelector": "c7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d2", "id": 219, "name": "Initialized", "nameLocation": "3514:11:1", "nodeType": "EventDefinition", "parameters": {"id": 218, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 217, "indexed": false, "mutability": "mutable", "name": "version", "nameLocation": "3533:7:1", "nodeType": "VariableDeclaration", "scope": 219, "src": "3526:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 216, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "3526:6:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "3525:16:1"}, "src": "3508:34:1"}, {"body": {"id": 301, "nodeType": "Block", "src": "4092:1079:1", "statements": [{"assignments": [224], "declarations": [{"constant": false, "id": 224, "mutability": "mutable", "name": "$", "nameLocation": "4187:1:1", "nodeType": "VariableDeclaration", "scope": 301, "src": "4158:30:1", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}, "typeName": {"id": 223, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 222, "name": "InitializableStorage", "nameLocations": ["4158:20:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 205, "src": "4158:20:1"}, "referencedDeclaration": 205, "src": "4158:20:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}}, "visibility": "internal"}], "id": 227, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 225, "name": "_getInitializableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "4191:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_InitializableStorage_$205_storage_ptr_$", "typeString": "function () pure returns (struct Initializable.InitializableStorage storage pointer)"}}, "id": 226, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4191:26:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "4158:59:1"}, {"assignments": [229], "declarations": [{"constant": false, "id": 229, "mutability": "mutable", "name": "isTopLevelCall", "nameLocation": "4284:14:1", "nodeType": "VariableDeclaration", "scope": 301, "src": "4279:19:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 228, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4279:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 233, "initialValue": {"id": 232, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4301:16:1", "subExpression": {"expression": {"id": 230, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 224, "src": "4302:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 231, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4304:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "4302:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "4279:38:1"}, {"assignments": [235], "declarations": [{"constant": false, "id": 235, "mutability": "mutable", "name": "initialized", "nameLocation": "4334:11:1", "nodeType": "VariableDeclaration", "scope": 301, "src": "4327:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 234, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "4327:6:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 238, "initialValue": {"expression": {"id": 236, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 224, "src": "4348:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 237, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4350:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "4348:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "4327:35:1"}, {"assignments": [240], "declarations": [{"constant": false, "id": 240, "mutability": "mutable", "name": "initialSetup", "nameLocation": "4709:12:1", "nodeType": "VariableDeclaration", "scope": 301, "src": "4704:17:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 239, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4704:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 246, "initialValue": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 245, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 243, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 241, "name": "initialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 235, "src": "4724:11:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 242, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4739:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4724:16:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"id": 244, "name": "isTopLevelCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 229, "src": "4744:14:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4724:34:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "4704:54:1"}, {"assignments": [248], "declarations": [{"constant": false, "id": 248, "mutability": "mutable", "name": "construction", "nameLocation": "4773:12:1", "nodeType": "VariableDeclaration", "scope": 301, "src": "4768:17:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 247, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4768:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 261, "initialValue": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 260, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 251, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 249, "name": "initialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 235, "src": "4788:11:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "31", "id": 250, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4803:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "4788:16:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 259, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"expression": {"arguments": [{"id": 254, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "4816:4:1", "typeDescriptions": {"typeIdentifier": "t_contract$_Initializable_$462", "typeString": "contract Initializable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Initializable_$462", "typeString": "contract Initializable"}], "id": 253, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4808:7:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 252, "name": "address", "nodeType": "ElementaryTypeName", "src": "4808:7:1", "typeDescriptions": {}}}, "id": 255, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4808:13:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 256, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4822:4:1", "memberName": "code", "nodeType": "MemberAccess", "src": "4808:18:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 257, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4827:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "4808:25:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 258, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4837:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4808:30:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4788:50:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "4768:70:1"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 266, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4853:13:1", "subExpression": {"id": 262, "name": "initialSetup", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 240, "src": "4854:12:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"id": 265, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4870:13:1", "subExpression": {"id": 264, "name": "construction", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 248, "src": "4871:12:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4853:30:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 271, "nodeType": "IfStatement", "src": "4849:91:1", "trueBody": {"id": 270, "nodeType": "Block", "src": "4885:55:1", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 267, "name": "InvalidInitialization", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 211, "src": "4906:21:1", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 268, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4906:23:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 269, "nodeType": "RevertStatement", "src": "4899:30:1"}]}}, {"expression": {"id": 276, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 272, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 224, "src": "4949:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 274, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4951:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "4949:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "31", "id": 275, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4966:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "4949:18:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 277, "nodeType": "ExpressionStatement", "src": "4949:18:1"}, {"condition": {"id": 278, "name": "isTopLevelCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 229, "src": "4981:14:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 286, "nodeType": "IfStatement", "src": "4977:67:1", "trueBody": {"id": 285, "nodeType": "Block", "src": "4997:47:1", "statements": [{"expression": {"id": 283, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 279, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 224, "src": "5011:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 281, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5013:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "5011:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 282, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5029:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "5011:22:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 284, "nodeType": "ExpressionStatement", "src": "5011:22:1"}]}}, {"id": 287, "nodeType": "PlaceholderStatement", "src": "5053:1:1"}, {"condition": {"id": 288, "name": "isTopLevelCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 229, "src": "5068:14:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 300, "nodeType": "IfStatement", "src": "5064:101:1", "trueBody": {"id": 299, "nodeType": "Block", "src": "5084:81:1", "statements": [{"expression": {"id": 293, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 289, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 224, "src": "5098:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 291, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5100:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "5098:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 292, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5116:5:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "5098:23:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 294, "nodeType": "ExpressionStatement", "src": "5098:23:1"}, {"eventCall": {"arguments": [{"hexValue": "31", "id": 296, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5152:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}], "id": 295, "name": "Initialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "5140:11:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint64_$returns$__$", "typeString": "function (uint64)"}}, "id": 297, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5140:14:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 298, "nodeType": "EmitStatement", "src": "5135:19:1"}]}}]}, "documentation": {"id": 220, "nodeType": "StructuredDocumentation", "src": "3548:516:1", "text": " @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\n `onlyInitializing` functions can be used to initialize parent contracts.\n Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\n number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\n production.\n Emits an {Initialized} event."}, "id": 302, "name": "initializer", "nameLocation": "4078:11:1", "nodeType": "ModifierDefinition", "parameters": {"id": 221, "nodeType": "ParameterList", "parameters": [], "src": "4089:2:1"}, "src": "4069:1102:1", "virtual": false, "visibility": "internal"}, {"body": {"id": 348, "nodeType": "Block", "src": "6289:392:1", "statements": [{"assignments": [309], "declarations": [{"constant": false, "id": 309, "mutability": "mutable", "name": "$", "nameLocation": "6384:1:1", "nodeType": "VariableDeclaration", "scope": 348, "src": "6355:30:1", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}, "typeName": {"id": 308, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 307, "name": "InitializableStorage", "nameLocations": ["6355:20:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 205, "src": "6355:20:1"}, "referencedDeclaration": 205, "src": "6355:20:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}}, "visibility": "internal"}], "id": 312, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 310, "name": "_getInitializableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "6388:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_InitializableStorage_$205_storage_ptr_$", "typeString": "function () pure returns (struct Initializable.InitializableStorage storage pointer)"}}, "id": 311, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6388:26:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "6355:59:1"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 319, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 313, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 309, "src": "6429:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 314, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6431:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "6429:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 318, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 315, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 309, "src": "6448:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 316, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6450:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "6448:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 317, "name": "version", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "6466:7:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "6448:25:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6429:44:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 324, "nodeType": "IfStatement", "src": "6425:105:1", "trueBody": {"id": 323, "nodeType": "Block", "src": "6475:55:1", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 320, "name": "InvalidInitialization", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 211, "src": "6496:21:1", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 321, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6496:23:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 322, "nodeType": "RevertStatement", "src": "6489:30:1"}]}}, {"expression": {"id": 329, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 325, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 309, "src": "6539:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 327, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6541:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "6539:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 328, "name": "version", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "6556:7:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "6539:24:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 330, "nodeType": "ExpressionStatement", "src": "6539:24:1"}, {"expression": {"id": 335, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 331, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 309, "src": "6573:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 333, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6575:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "6573:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 334, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6591:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "6573:22:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 336, "nodeType": "ExpressionStatement", "src": "6573:22:1"}, {"id": 337, "nodeType": "PlaceholderStatement", "src": "6605:1:1"}, {"expression": {"id": 342, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 338, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 309, "src": "6616:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 340, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6618:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "6616:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 341, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6634:5:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "6616:23:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 343, "nodeType": "ExpressionStatement", "src": "6616:23:1"}, {"eventCall": {"arguments": [{"id": 345, "name": "version", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "6666:7:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 344, "name": "Initialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "6654:11:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint64_$returns$__$", "typeString": "function (uint64)"}}, "id": 346, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6654:20:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 347, "nodeType": "EmitStatement", "src": "6649:25:1"}]}, "documentation": {"id": 303, "nodeType": "StructuredDocumentation", "src": "5177:1068:1", "text": " @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\n contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\n used to initialize parent contracts.\n A reinitializer may be used after the original initialization step. This is essential to configure modules that\n are added through upgrades and that require initialization.\n When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\n cannot be nested. If one is invoked in the context of another, execution will revert.\n Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\n a contract, executing them in the right order is up to the developer or operator.\n WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\n Emits an {Initialized} event."}, "id": 349, "name": "reinitializer", "nameLocation": "6259:13:1", "nodeType": "ModifierDefinition", "parameters": {"id": 306, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 305, "mutability": "mutable", "name": "version", "nameLocation": "6280:7:1", "nodeType": "VariableDeclaration", "scope": 349, "src": "6273:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 304, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "6273:6:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "6272:16:1"}, "src": "6250:431:1", "virtual": false, "visibility": "internal"}, {"body": {"id": 356, "nodeType": "Block", "src": "6919:48:1", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 352, "name": "_checkInitializing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 370, "src": "6929:18:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 353, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6929:20:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 354, "nodeType": "ExpressionStatement", "src": "6929:20:1"}, {"id": 355, "nodeType": "PlaceholderStatement", "src": "6959:1:1"}]}, "documentation": {"id": 350, "nodeType": "StructuredDocumentation", "src": "6687:199:1", "text": " @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\n {initializer} and {reinitializer} modifiers, directly or indirectly."}, "id": 357, "name": "onlyInitializing", "nameLocation": "6900:16:1", "nodeType": "ModifierDefinition", "parameters": {"id": 351, "nodeType": "ParameterList", "parameters": [], "src": "6916:2:1"}, "src": "6891:76:1", "virtual": false, "visibility": "internal"}, {"body": {"id": 369, "nodeType": "Block", "src": "7134:89:1", "statements": [{"condition": {"id": 363, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "7148:18:1", "subExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 361, "name": "_isInitializing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 438, "src": "7149:15:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 362, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7149:17:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 368, "nodeType": "IfStatement", "src": "7144:73:1", "trueBody": {"id": 367, "nodeType": "Block", "src": "7168:49:1", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 364, "name": "NotInitializing", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 214, "src": "7189:15:1", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 365, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7189:17:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 366, "nodeType": "RevertStatement", "src": "7182:24:1"}]}}]}, "documentation": {"id": 358, "nodeType": "StructuredDocumentation", "src": "6973:104:1", "text": " @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}."}, "id": 370, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkInitializing", "nameLocation": "7091:18:1", "nodeType": "FunctionDefinition", "parameters": {"id": 359, "nodeType": "ParameterList", "parameters": [], "src": "7109:2:1"}, "returnParameters": {"id": 360, "nodeType": "ParameterList", "parameters": [], "src": "7134:0:1"}, "scope": 462, "src": "7082:141:1", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 415, "nodeType": "Block", "src": "7758:373:1", "statements": [{"assignments": [376], "declarations": [{"constant": false, "id": 376, "mutability": "mutable", "name": "$", "nameLocation": "7853:1:1", "nodeType": "VariableDeclaration", "scope": 415, "src": "7824:30:1", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}, "typeName": {"id": 375, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 374, "name": "InitializableStorage", "nameLocations": ["7824:20:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 205, "src": "7824:20:1"}, "referencedDeclaration": 205, "src": "7824:20:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}}, "visibility": "internal"}], "id": 379, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 377, "name": "_getInitializableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "7857:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_InitializableStorage_$205_storage_ptr_$", "typeString": "function () pure returns (struct Initializable.InitializableStorage storage pointer)"}}, "id": 378, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7857:26:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "7824:59:1"}, {"condition": {"expression": {"id": 380, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "7898:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 381, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7900:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "7898:15:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 386, "nodeType": "IfStatement", "src": "7894:76:1", "trueBody": {"id": 385, "nodeType": "Block", "src": "7915:55:1", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 382, "name": "InvalidInitialization", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 211, "src": "7936:21:1", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 383, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7936:23:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 384, "nodeType": "RevertStatement", "src": "7929:30:1"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "id": 394, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 387, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "7983:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 388, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7985:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "7983:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"expression": {"arguments": [{"id": 391, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8006:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 390, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "8006:6:1", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}], "id": 389, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "8001:4:1", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 392, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8001:12:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint64", "typeString": "type(uint64)"}}, "id": 393, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "8014:3:1", "memberName": "max", "nodeType": "MemberAccess", "src": "8001:16:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "7983:34:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 414, "nodeType": "IfStatement", "src": "7979:146:1", "trueBody": {"id": 413, "nodeType": "Block", "src": "8019:106:1", "statements": [{"expression": {"id": 403, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 395, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "8033:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 397, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "8035:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "8033:14:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"arguments": [{"id": 400, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8055:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 399, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "8055:6:1", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}], "id": 398, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "8050:4:1", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 401, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8050:12:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint64", "typeString": "type(uint64)"}}, "id": 402, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "8063:3:1", "memberName": "max", "nodeType": "MemberAccess", "src": "8050:16:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "8033:33:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 404, "nodeType": "ExpressionStatement", "src": "8033:33:1"}, {"eventCall": {"arguments": [{"expression": {"arguments": [{"id": 408, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "8102:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 407, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "8102:6:1", "typeDescriptions": {}}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}], "id": 406, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "8097:4:1", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 409, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8097:12:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_uint64", "typeString": "type(uint64)"}}, "id": 410, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "8110:3:1", "memberName": "max", "nodeType": "MemberAccess", "src": "8097:16:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 405, "name": "Initialized", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "8085:11:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint64_$returns$__$", "typeString": "function (uint64)"}}, "id": 411, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8085:29:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 412, "nodeType": "EmitStatement", "src": "8080:34:1"}]}}]}, "documentation": {"id": 371, "nodeType": "StructuredDocumentation", "src": "7229:475:1", "text": " @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\n Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\n to any version. It is recommended to use this to lock implementation contracts that are designed to be called\n through proxies.\n Emits an {Initialized} event the first time it is successfully executed."}, "id": 416, "implemented": true, "kind": "function", "modifiers": [], "name": "_disableInitializers", "nameLocation": "7718:20:1", "nodeType": "FunctionDefinition", "parameters": {"id": 372, "nodeType": "ParameterList", "parameters": [], "src": "7738:2:1"}, "returnParameters": {"id": 373, "nodeType": "ParameterList", "parameters": [], "src": "7758:0:1"}, "scope": 462, "src": "7709:422:1", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 426, "nodeType": "Block", "src": "8306:63:1", "statements": [{"expression": {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 422, "name": "_getInitializableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "8323:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_InitializableStorage_$205_storage_ptr_$", "typeString": "function () pure returns (struct Initializable.InitializableStorage storage pointer)"}}, "id": 423, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8323:26:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 424, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8350:12:1", "memberName": "_initialized", "nodeType": "MemberAccess", "referencedDeclaration": 201, "src": "8323:39:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "functionReturnParameters": 421, "id": 425, "nodeType": "Return", "src": "8316:46:1"}]}, "documentation": {"id": 417, "nodeType": "StructuredDocumentation", "src": "8137:99:1", "text": " @dev Returns the highest version that has been initialized. See {reinitializer}."}, "id": 427, "implemented": true, "kind": "function", "modifiers": [], "name": "_getInitializedVersion", "nameLocation": "8250:22:1", "nodeType": "FunctionDefinition", "parameters": {"id": 418, "nodeType": "ParameterList", "parameters": [], "src": "8272:2:1"}, "returnParameters": {"id": 421, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 420, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 427, "src": "8298:6:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 419, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "8298:6:1", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "8297:8:1"}, "scope": 462, "src": "8241:128:1", "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"body": {"id": 437, "nodeType": "Block", "src": "8541:64:1", "statements": [{"expression": {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 433, "name": "_getInitializableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "8558:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_InitializableStorage_$205_storage_ptr_$", "typeString": "function () pure returns (struct Initializable.InitializableStorage storage pointer)"}}, "id": 434, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8558:26:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage storage pointer"}}, "id": 435, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8585:13:1", "memberName": "_initializing", "nodeType": "MemberAccess", "referencedDeclaration": 204, "src": "8558:40:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 432, "id": 436, "nodeType": "Return", "src": "8551:47:1"}]}, "documentation": {"id": 428, "nodeType": "StructuredDocumentation", "src": "8375:105:1", "text": " @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}."}, "id": 438, "implemented": true, "kind": "function", "modifiers": [], "name": "_isInitializing", "nameLocation": "8494:15:1", "nodeType": "FunctionDefinition", "parameters": {"id": 429, "nodeType": "ParameterList", "parameters": [], "src": "8509:2:1"}, "returnParameters": {"id": 432, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 431, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 438, "src": "8535:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 430, "name": "bool", "nodeType": "ElementaryTypeName", "src": "8535:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "8534:6:1"}, "scope": 462, "src": "8485:120:1", "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"body": {"id": 446, "nodeType": "Block", "src": "8896:45:1", "statements": [{"expression": {"id": 444, "name": "INITIALIZABLE_STORAGE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 208, "src": "8913:21:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 443, "id": 445, "nodeType": "Return", "src": "8906:28:1"}]}, "documentation": {"id": 439, "nodeType": "StructuredDocumentation", "src": "8611:203:1", "text": " @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\n NOTE: Consider following the ERC-7201 formula to derive storage locations."}, "id": 447, "implemented": true, "kind": "function", "modifiers": [], "name": "_initializableStorageSlot", "nameLocation": "8828:25:1", "nodeType": "FunctionDefinition", "parameters": {"id": 440, "nodeType": "ParameterList", "parameters": [], "src": "8853:2:1"}, "returnParameters": {"id": 443, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 442, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 447, "src": "8887:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 441, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8887:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "8886:9:1"}, "scope": 462, "src": "8819:122:1", "stateMutability": "pure", "virtual": true, "visibility": "internal"}, {"body": {"id": 460, "nodeType": "Block", "src": "9161:115:1", "statements": [{"assignments": [455], "declarations": [{"constant": false, "id": 455, "mutability": "mutable", "name": "slot", "nameLocation": "9179:4:1", "nodeType": "VariableDeclaration", "scope": 460, "src": "9171:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 454, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "9171:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 458, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 456, "name": "_initializableStorageSlot", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 447, "src": "9186:25:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_bytes32_$", "typeString": "function () pure returns (bytes32)"}}, "id": 457, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "9186:27:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "9171:42:1"}, {"AST": {"nativeSrc": "9232:38:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9232:38:1", "statements": [{"nativeSrc": "9246:14:1", "nodeType": "YulAssignment", "src": "9246:14:1", "value": {"name": "slot", "nativeSrc": "9256:4:1", "nodeType": "YulIdentifier", "src": "9256:4:1"}, "variableNames": [{"name": "$.slot", "nativeSrc": "9246:6:1", "nodeType": "YulIdentifier", "src": "9246:6:1"}]}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 452, "isOffset": false, "isSlot": true, "src": "9246:6:1", "suffix": "slot", "valueSize": 1}, {"declaration": 455, "isOffset": false, "isSlot": false, "src": "9256:4:1", "valueSize": 1}], "id": 459, "nodeType": "InlineAssembly", "src": "9223:47:1"}]}, "documentation": {"id": 448, "nodeType": "StructuredDocumentation", "src": "8947:67:1", "text": " @dev Returns a pointer to the storage namespace."}, "id": 461, "implemented": true, "kind": "function", "modifiers": [], "name": "_getInitializableStorage", "nameLocation": "9080:24:1", "nodeType": "FunctionDefinition", "parameters": {"id": 449, "nodeType": "ParameterList", "parameters": [], "src": "9104:2:1"}, "returnParameters": {"id": 453, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 452, "mutability": "mutable", "name": "$", "nameLocation": "9158:1:1", "nodeType": "VariableDeclaration", "scope": 461, "src": "9129:30:1", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}, "typeName": {"id": 451, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 450, "name": "InitializableStorage", "nameLocations": ["9129:20:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 205, "src": "9129:20:1"}, "referencedDeclaration": 205, "src": "9129:20:1", "typeDescriptions": {"typeIdentifier": "t_struct$_InitializableStorage_$205_storage_ptr", "typeString": "struct Initializable.InitializableStorage"}}, "visibility": "internal"}], "src": "9128:32:1"}, "scope": 462, "src": "9071:205:1", "stateMutability": "pure", "virtual": false, "visibility": "private"}], "scope": 463, "src": "2349:6929:1", "usedErrors": [211, 214], "usedEvents": [219]}], "src": "113:9166:1"}, "id": 1}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "exportedSymbols": {"ContextUpgradeable": [508], "Initializable": [462]}, "id": 509, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 464, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:2"}, {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "file": "../proxy/utils/Initializable.sol", "id": 466, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 509, "sourceUnit": 463, "src": "126:63:2", "symbolAliases": [{"foreign": {"id": 465, "name": "Initializable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "134:13:2", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 468, "name": "Initializable", "nameLocations": ["728:13:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 462, "src": "728:13:2"}, "id": 469, "nodeType": "InheritanceSpecifier", "src": "728:13:2"}], "canonicalName": "ContextUpgradeable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 467, "nodeType": "StructuredDocumentation", "src": "191:496:2", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 508, "linearizedBaseContracts": [508, 462], "name": "ContextUpgradeable", "nameLocation": "706:18:2", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 474, "nodeType": "Block", "src": "800:7:2", "statements": []}, "id": 475, "implemented": true, "kind": "function", "modifiers": [{"id": 472, "kind": "modifierInvocation", "modifierName": {"id": 471, "name": "onlyInitializing", "nameLocations": ["783:16:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "783:16:2"}, "nodeType": "ModifierInvocation", "src": "783:16:2"}], "name": "__Context_init", "nameLocation": "757:14:2", "nodeType": "FunctionDefinition", "parameters": {"id": 470, "nodeType": "ParameterList", "parameters": [], "src": "771:2:2"}, "returnParameters": {"id": 473, "nodeType": "ParameterList", "parameters": [], "src": "800:0:2"}, "scope": 508, "src": "748:59:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 480, "nodeType": "Block", "src": "875:7:2", "statements": []}, "id": 481, "implemented": true, "kind": "function", "modifiers": [{"id": 478, "kind": "modifierInvocation", "modifierName": {"id": 477, "name": "onlyInitializing", "nameLocations": ["858:16:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "858:16:2"}, "nodeType": "ModifierInvocation", "src": "858:16:2"}], "name": "__Context_init_unchained", "nameLocation": "822:24:2", "nodeType": "FunctionDefinition", "parameters": {"id": 476, "nodeType": "ParameterList", "parameters": [], "src": "846:2:2"}, "returnParameters": {"id": 479, "nodeType": "ParameterList", "parameters": [], "src": "875:0:2"}, "scope": 508, "src": "813:69:2", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 489, "nodeType": "Block", "src": "949:34:2", "statements": [{"expression": {"expression": {"id": 486, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "966:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 487, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "970:6:2", "memberName": "sender", "nodeType": "MemberAccess", "src": "966:10:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 485, "id": 488, "nodeType": "Return", "src": "959:17:2"}]}, "id": 490, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "896:10:2", "nodeType": "FunctionDefinition", "parameters": {"id": 482, "nodeType": "ParameterList", "parameters": [], "src": "906:2:2"}, "returnParameters": {"id": 485, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 484, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 490, "src": "940:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 483, "name": "address", "nodeType": "ElementaryTypeName", "src": "940:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "939:9:2"}, "scope": 508, "src": "887:96:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 498, "nodeType": "Block", "src": "1056:32:2", "statements": [{"expression": {"expression": {"id": 495, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1073:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 496, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1077:4:2", "memberName": "data", "nodeType": "MemberAccess", "src": "1073:8:2", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 494, "id": 497, "nodeType": "Return", "src": "1066:15:2"}]}, "id": 499, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "998:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 491, "nodeType": "ParameterList", "parameters": [], "src": "1006:2:2"}, "returnParameters": {"id": 494, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 493, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 499, "src": "1040:14:2", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 492, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1040:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "1039:16:2"}, "scope": 508, "src": "989:99:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 506, "nodeType": "Block", "src": "1166:25:2", "statements": [{"expression": {"hexValue": "30", "id": 504, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1183:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 503, "id": 505, "nodeType": "Return", "src": "1176:8:2"}]}, "id": 507, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "1103:20:2", "nodeType": "FunctionDefinition", "parameters": {"id": 500, "nodeType": "ParameterList", "parameters": [], "src": "1123:2:2"}, "returnParameters": {"id": 503, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 502, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 507, "src": "1157:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 501, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1157:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1156:9:2"}, "scope": 508, "src": "1094:97:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 509, "src": "688:505:2", "usedErrors": [211, 214], "usedEvents": [219]}], "src": "101:1093:2"}, "id": 2}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol", "exportedSymbols": {"ContextUpgradeable": [508], "Initializable": [462], "PausableUpgradeable": [668]}, "id": 669, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 510, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:3"}, {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "file": "../utils/ContextUpgradeable.sol", "id": 512, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 669, "sourceUnit": 509, "src": "128:67:3", "symbolAliases": [{"foreign": {"id": 511, "name": "ContextUpgradeable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 508, "src": "136:18:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "file": "../proxy/utils/Initializable.sol", "id": 514, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 669, "sourceUnit": 463, "src": "196:63:3", "symbolAliases": [{"foreign": {"id": 513, "name": "Initializable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "204:13:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 516, "name": "Initializable", "nameLocations": ["742:13:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 462, "src": "742:13:3"}, "id": 517, "nodeType": "InheritanceSpecifier", "src": "742:13:3"}, {"baseName": {"id": 518, "name": "ContextUpgradeable", "nameLocations": ["757:18:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 508, "src": "757:18:3"}, "id": 519, "nodeType": "InheritanceSpecifier", "src": "757:18:3"}], "canonicalName": "PausableUpgradeable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 515, "nodeType": "StructuredDocumentation", "src": "261:439:3", "text": " @dev Contract module which allows children to implement an emergency stop\n mechanism that can be triggered by an authorized account.\n This module is used through inheritance. It will make available the\n modifiers `whenNotPaused` and `whenPaused`, which can be applied to\n the functions of your contract. Note that they will not be pausable by\n simply including this module, only once the modifiers are put in place."}, "fullyImplemented": true, "id": 668, "linearizedBaseContracts": [668, 508, 462], "name": "PausableUpgradeable", "nameLocation": "719:19:3", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "PausableUpgradeable.PausableStorage", "documentation": {"id": 520, "nodeType": "StructuredDocumentation", "src": "782:66:3", "text": "@custom:storage-location erc7201:openzeppelin.storage.Pausable"}, "id": 523, "members": [{"constant": false, "id": 522, "mutability": "mutable", "name": "_paused", "nameLocation": "891:7:3", "nodeType": "VariableDeclaration", "scope": 523, "src": "886:12:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 521, "name": "bool", "nodeType": "ElementaryTypeName", "src": "886:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "PausableStorage", "nameLocation": "860:15:3", "nodeType": "StructDefinition", "scope": 668, "src": "853:52:3", "visibility": "public"}, {"constant": true, "id": 526, "mutability": "constant", "name": "PausableStorageLocation", "nameLocation": "1048:23:3", "nodeType": "VariableDeclaration", "scope": 668, "src": "1023:117:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 524, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1023:7:3", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"hexValue": "307863643565643135633665313837653737653961656538383138346332316634663231383261623538323763623362376530376662656463643633663033333030", "id": 525, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1074:66:3", "typeDescriptions": {"typeIdentifier": "t_rational_92891662540554778686986514950364265630913525426840345632122912437671245656832_by_1", "typeString": "int_const 9289...(69 digits omitted)...6832"}, "value": "0xcd5ed15c6e187e77e9aee88184c21f4f2182ab5827cb3b7e07fbedcd63f03300"}, "visibility": "private"}, {"body": {"id": 533, "nodeType": "Block", "src": "1227:82:3", "statements": [{"AST": {"nativeSrc": "1246:57:3", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1246:57:3", "statements": [{"nativeSrc": "1260:33:3", "nodeType": "YulAssignment", "src": "1260:33:3", "value": {"name": "PausableStorageLocation", "nativeSrc": "1270:23:3", "nodeType": "YulIdentifier", "src": "1270:23:3"}, "variableNames": [{"name": "$.slot", "nativeSrc": "1260:6:3", "nodeType": "YulIdentifier", "src": "1260:6:3"}]}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 530, "isOffset": false, "isSlot": true, "src": "1260:6:3", "suffix": "slot", "valueSize": 1}, {"declaration": 526, "isOffset": false, "isSlot": false, "src": "1270:23:3", "valueSize": 1}], "id": 532, "nodeType": "InlineAssembly", "src": "1237:66:3"}]}, "id": 534, "implemented": true, "kind": "function", "modifiers": [], "name": "_getPausableStorage", "nameLocation": "1156:19:3", "nodeType": "FunctionDefinition", "parameters": {"id": 527, "nodeType": "ParameterList", "parameters": [], "src": "1175:2:3"}, "returnParameters": {"id": 531, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 530, "mutability": "mutable", "name": "$", "nameLocation": "1224:1:3", "nodeType": "VariableDeclaration", "scope": 534, "src": "1200:25:3", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}, "typeName": {"id": 529, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 528, "name": "PausableStorage", "nameLocations": ["1200:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 523, "src": "1200:15:3"}, "referencedDeclaration": 523, "src": "1200:15:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}}, "visibility": "internal"}], "src": "1199:27:3"}, "scope": 668, "src": "1147:162:3", "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"anonymous": false, "documentation": {"id": 535, "nodeType": "StructuredDocumentation", "src": "1315:73:3", "text": " @dev Emitted when the pause is triggered by `account`."}, "eventSelector": "62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258", "id": 539, "name": "Paused", "nameLocation": "1399:6:3", "nodeType": "EventDefinition", "parameters": {"id": 538, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 537, "indexed": false, "mutability": "mutable", "name": "account", "nameLocation": "1414:7:3", "nodeType": "VariableDeclaration", "scope": 539, "src": "1406:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 536, "name": "address", "nodeType": "ElementaryTypeName", "src": "1406:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1405:17:3"}, "src": "1393:30:3"}, {"anonymous": false, "documentation": {"id": 540, "nodeType": "StructuredDocumentation", "src": "1429:70:3", "text": " @dev Emitted when the pause is lifted by `account`."}, "eventSelector": "5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa", "id": 544, "name": "Unpaused", "nameLocation": "1510:8:3", "nodeType": "EventDefinition", "parameters": {"id": 543, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 542, "indexed": false, "mutability": "mutable", "name": "account", "nameLocation": "1527:7:3", "nodeType": "VariableDeclaration", "scope": 544, "src": "1519:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 541, "name": "address", "nodeType": "ElementaryTypeName", "src": "1519:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1518:17:3"}, "src": "1504:32:3"}, {"documentation": {"id": 545, "nodeType": "StructuredDocumentation", "src": "1542:76:3", "text": " @dev The operation failed because the contract is paused."}, "errorSelector": "d93c0665", "id": 547, "name": "EnforcedPause", "nameLocation": "1629:13:3", "nodeType": "ErrorDefinition", "parameters": {"id": 546, "nodeType": "ParameterList", "parameters": [], "src": "1642:2:3"}, "src": "1623:22:3"}, {"documentation": {"id": 548, "nodeType": "StructuredDocumentation", "src": "1651:80:3", "text": " @dev The operation failed because the contract is not paused."}, "errorSelector": "8dfc202b", "id": 550, "name": "ExpectedPause", "nameLocation": "1742:13:3", "nodeType": "ErrorDefinition", "parameters": {"id": 549, "nodeType": "ParameterList", "parameters": [], "src": "1755:2:3"}, "src": "1736:22:3"}, {"body": {"id": 557, "nodeType": "Block", "src": "1969:47:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 553, "name": "_requireNotPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 606, "src": "1979:17:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 554, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1979:19:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 555, "nodeType": "ExpressionStatement", "src": "1979:19:3"}, {"id": 556, "nodeType": "PlaceholderStatement", "src": "2008:1:3"}]}, "documentation": {"id": 551, "nodeType": "StructuredDocumentation", "src": "1764:175:3", "text": " @dev Modifier to make a function callable only when the contract is not paused.\n Requirements:\n - The contract must not be paused."}, "id": 558, "name": "whenNotPaused", "nameLocation": "1953:13:3", "nodeType": "ModifierDefinition", "parameters": {"id": 552, "nodeType": "ParameterList", "parameters": [], "src": "1966:2:3"}, "src": "1944:72:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 565, "nodeType": "Block", "src": "2216:44:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 561, "name": "_requirePaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 619, "src": "2226:14:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 562, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2226:16:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 563, "nodeType": "ExpressionStatement", "src": "2226:16:3"}, {"id": 564, "nodeType": "PlaceholderStatement", "src": "2252:1:3"}]}, "documentation": {"id": 559, "nodeType": "StructuredDocumentation", "src": "2022:167:3", "text": " @dev Modifier to make a function callable only when the contract is paused.\n Requirements:\n - The contract must be paused."}, "id": 566, "name": "whenPaused", "nameLocation": "2203:10:3", "nodeType": "ModifierDefinition", "parameters": {"id": 560, "nodeType": "ParameterList", "parameters": [], "src": "2213:2:3"}, "src": "2194:66:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 571, "nodeType": "Block", "src": "2319:7:3", "statements": []}, "id": 572, "implemented": true, "kind": "function", "modifiers": [{"id": 569, "kind": "modifierInvocation", "modifierName": {"id": 568, "name": "onlyInitializing", "nameLocations": ["2302:16:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "2302:16:3"}, "nodeType": "ModifierInvocation", "src": "2302:16:3"}], "name": "__Pausable_init", "nameLocation": "2275:15:3", "nodeType": "FunctionDefinition", "parameters": {"id": 567, "nodeType": "ParameterList", "parameters": [], "src": "2290:2:3"}, "returnParameters": {"id": 570, "nodeType": "ParameterList", "parameters": [], "src": "2319:0:3"}, "scope": 668, "src": "2266:60:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 577, "nodeType": "Block", "src": "2395:7:3", "statements": []}, "id": 578, "implemented": true, "kind": "function", "modifiers": [{"id": 575, "kind": "modifierInvocation", "modifierName": {"id": 574, "name": "onlyInitializing", "nameLocations": ["2378:16:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "2378:16:3"}, "nodeType": "ModifierInvocation", "src": "2378:16:3"}], "name": "__Pausable_init_unchained", "nameLocation": "2341:25:3", "nodeType": "FunctionDefinition", "parameters": {"id": 573, "nodeType": "ParameterList", "parameters": [], "src": "2366:2:3"}, "returnParameters": {"id": 576, "nodeType": "ParameterList", "parameters": [], "src": "2395:0:3"}, "scope": 668, "src": "2332:70:3", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 593, "nodeType": "Block", "src": "2549:92:3", "statements": [{"assignments": [586], "declarations": [{"constant": false, "id": 586, "mutability": "mutable", "name": "$", "nameLocation": "2583:1:3", "nodeType": "VariableDeclaration", "scope": 593, "src": "2559:25:3", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}, "typeName": {"id": 585, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 584, "name": "PausableStorage", "nameLocations": ["2559:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 523, "src": "2559:15:3"}, "referencedDeclaration": 523, "src": "2559:15:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}}, "visibility": "internal"}], "id": 589, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 587, "name": "_getPausableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "2587:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_PausableStorage_$523_storage_ptr_$", "typeString": "function () pure returns (struct PausableUpgradeable.PausableStorage storage pointer)"}}, "id": 588, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2587:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "2559:49:3"}, {"expression": {"expression": {"id": 590, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 586, "src": "2625:1:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "id": 591, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2627:7:3", "memberName": "_paused", "nodeType": "MemberAccess", "referencedDeclaration": 522, "src": "2625:9:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 583, "id": 592, "nodeType": "Return", "src": "2618:16:3"}]}, "documentation": {"id": 579, "nodeType": "StructuredDocumentation", "src": "2407:84:3", "text": " @dev Returns true if the contract is paused, and false otherwise."}, "functionSelector": "5c975abb", "id": 594, "implemented": true, "kind": "function", "modifiers": [], "name": "paused", "nameLocation": "2505:6:3", "nodeType": "FunctionDefinition", "parameters": {"id": 580, "nodeType": "ParameterList", "parameters": [], "src": "2511:2:3"}, "returnParameters": {"id": 583, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 582, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 594, "src": "2543:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 581, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2543:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2542:6:3"}, "scope": 668, "src": "2496:145:3", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 605, "nodeType": "Block", "src": "2760:77:3", "statements": [{"condition": {"arguments": [], "expression": {"argumentTypes": [], "id": 598, "name": "paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 594, "src": "2774:6:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 599, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2774:8:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 604, "nodeType": "IfStatement", "src": "2770:61:3", "trueBody": {"id": 603, "nodeType": "Block", "src": "2784:47:3", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 600, "name": "EnforcedPause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 547, "src": "2805:13:3", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 601, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2805:15:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 602, "nodeType": "RevertStatement", "src": "2798:22:3"}]}}]}, "documentation": {"id": 595, "nodeType": "StructuredDocumentation", "src": "2647:57:3", "text": " @dev Throws if the contract is paused."}, "id": 606, "implemented": true, "kind": "function", "modifiers": [], "name": "_requireNotPaused", "nameLocation": "2718:17:3", "nodeType": "FunctionDefinition", "parameters": {"id": 596, "nodeType": "ParameterList", "parameters": [], "src": "2735:2:3"}, "returnParameters": {"id": 597, "nodeType": "ParameterList", "parameters": [], "src": "2760:0:3"}, "scope": 668, "src": "2709:128:3", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 618, "nodeType": "Block", "src": "2957:78:3", "statements": [{"condition": {"id": 612, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "2971:9:3", "subExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 610, "name": "paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 594, "src": "2972:6:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 611, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2972:8:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 617, "nodeType": "IfStatement", "src": "2967:62:3", "trueBody": {"id": 616, "nodeType": "Block", "src": "2982:47:3", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 613, "name": "ExpectedPause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 550, "src": "3003:13:3", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 614, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3003:15:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 615, "nodeType": "RevertStatement", "src": "2996:22:3"}]}}]}, "documentation": {"id": 607, "nodeType": "StructuredDocumentation", "src": "2843:61:3", "text": " @dev Throws if the contract is not paused."}, "id": 619, "implemented": true, "kind": "function", "modifiers": [], "name": "_requirePaused", "nameLocation": "2918:14:3", "nodeType": "FunctionDefinition", "parameters": {"id": 608, "nodeType": "ParameterList", "parameters": [], "src": "2932:2:3"}, "returnParameters": {"id": 609, "nodeType": "ParameterList", "parameters": [], "src": "2957:0:3"}, "scope": 668, "src": "2909:126:3", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 642, "nodeType": "Block", "src": "3219:127:3", "statements": [{"assignments": [627], "declarations": [{"constant": false, "id": 627, "mutability": "mutable", "name": "$", "nameLocation": "3253:1:3", "nodeType": "VariableDeclaration", "scope": 642, "src": "3229:25:3", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}, "typeName": {"id": 626, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 625, "name": "PausableStorage", "nameLocations": ["3229:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 523, "src": "3229:15:3"}, "referencedDeclaration": 523, "src": "3229:15:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}}, "visibility": "internal"}], "id": 630, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 628, "name": "_getPausableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "3257:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_PausableStorage_$523_storage_ptr_$", "typeString": "function () pure returns (struct PausableUpgradeable.PausableStorage storage pointer)"}}, "id": 629, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3257:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "3229:49:3"}, {"expression": {"id": 635, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 631, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 627, "src": "3288:1:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "id": 633, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "3290:7:3", "memberName": "_paused", "nodeType": "MemberAccess", "referencedDeclaration": 522, "src": "3288:9:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 634, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3300:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "3288:16:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 636, "nodeType": "ExpressionStatement", "src": "3288:16:3"}, {"eventCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 638, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 490, "src": "3326:10:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 639, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3326:12:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 637, "name": "Paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 539, "src": "3319:6:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 640, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3319:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 641, "nodeType": "EmitStatement", "src": "3314:25:3"}]}, "documentation": {"id": 620, "nodeType": "StructuredDocumentation", "src": "3041:124:3", "text": " @dev Triggers stopped state.\n Requirements:\n - The contract must not be paused."}, "id": 643, "implemented": true, "kind": "function", "modifiers": [{"id": 623, "kind": "modifierInvocation", "modifierName": {"id": 622, "name": "whenNotPaused", "nameLocations": ["3205:13:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 558, "src": "3205:13:3"}, "nodeType": "ModifierInvocation", "src": "3205:13:3"}], "name": "_pause", "nameLocation": "3179:6:3", "nodeType": "FunctionDefinition", "parameters": {"id": 621, "nodeType": "ParameterList", "parameters": [], "src": "3185:2:3"}, "returnParameters": {"id": 624, "nodeType": "ParameterList", "parameters": [], "src": "3219:0:3"}, "scope": 668, "src": "3170:176:3", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 666, "nodeType": "Block", "src": "3526:130:3", "statements": [{"assignments": [651], "declarations": [{"constant": false, "id": 651, "mutability": "mutable", "name": "$", "nameLocation": "3560:1:3", "nodeType": "VariableDeclaration", "scope": 666, "src": "3536:25:3", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}, "typeName": {"id": 650, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 649, "name": "PausableStorage", "nameLocations": ["3536:15:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 523, "src": "3536:15:3"}, "referencedDeclaration": 523, "src": "3536:15:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage"}}, "visibility": "internal"}], "id": 654, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 652, "name": "_getPausableStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "3564:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_PausableStorage_$523_storage_ptr_$", "typeString": "function () pure returns (struct PausableUpgradeable.PausableStorage storage pointer)"}}, "id": 653, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3564:21:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "3536:49:3"}, {"expression": {"id": 659, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 655, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 651, "src": "3595:1:3", "typeDescriptions": {"typeIdentifier": "t_struct$_PausableStorage_$523_storage_ptr", "typeString": "struct PausableUpgradeable.PausableStorage storage pointer"}}, "id": 657, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "3597:7:3", "memberName": "_paused", "nodeType": "MemberAccess", "referencedDeclaration": 522, "src": "3595:9:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 658, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3607:5:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "3595:17:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 660, "nodeType": "ExpressionStatement", "src": "3595:17:3"}, {"eventCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 662, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 490, "src": "3636:10:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 663, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3636:12:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 661, "name": "Unpaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 544, "src": "3627:8:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 664, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3627:22:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 665, "nodeType": "EmitStatement", "src": "3622:27:3"}]}, "documentation": {"id": 644, "nodeType": "StructuredDocumentation", "src": "3352:121:3", "text": " @dev Returns to normal state.\n Requirements:\n - The contract must be paused."}, "id": 667, "implemented": true, "kind": "function", "modifiers": [{"id": 647, "kind": "modifierInvocation", "modifierName": {"id": 646, "name": "whenPaused", "nameLocations": ["3515:10:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 566, "src": "3515:10:3"}, "nodeType": "ModifierInvocation", "src": "3515:10:3"}], "name": "_unpause", "nameLocation": "3487:8:3", "nodeType": "FunctionDefinition", "parameters": {"id": 645, "nodeType": "ParameterList", "parameters": [], "src": "3495:2:3"}, "returnParameters": {"id": 648, "nodeType": "ParameterList", "parameters": [], "src": "3526:0:3"}, "scope": 668, "src": "3478:178:3", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 669, "src": "701:2957:3", "usedErrors": [211, 214, 547, 550], "usedEvents": [219, 539, 544]}], "src": "102:3557:3"}, "id": 3}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "exportedSymbols": {"Initializable": [462], "ReentrancyGuardUpgradeable": [797]}, "id": 798, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 670, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "109:24:4"}, {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "file": "../proxy/utils/Initializable.sol", "id": 672, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 798, "sourceUnit": 463, "src": "134:63:4", "symbolAliases": [{"foreign": {"id": 671, "name": "Initializable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "142:13:4", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 674, "name": "Initializable", "nameLocations": ["1142:13:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 462, "src": "1142:13:4"}, "id": 675, "nodeType": "InheritanceSpecifier", "src": "1142:13:4"}], "canonicalName": "ReentrancyGuardUpgradeable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 673, "nodeType": "StructuredDocumentation", "src": "199:894:4", "text": " @dev Contract module that helps prevent reentrant calls to a function.\n Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n available, which can be applied to functions to make sure there are no nested\n (reentrant) calls to them.\n Note that because there is a single `nonReentrant` guard, functions marked as\n `nonReentrant` may not call one another. This can be worked around by making\n those functions `private`, and then adding `external` `nonReentrant` entry\n points to them.\n TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n consider using {ReentrancyGuardTransient} instead.\n TIP: If you would like to learn more about reentrancy and alternative ways\n to protect against it, check out our blog post\n https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul]."}, "fullyImplemented": true, "id": 797, "linearizedBaseContracts": [797, 462], "name": "ReentrancyGuardUpgradeable", "nameLocation": "1112:26:4", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 678, "mutability": "constant", "name": "NOT_ENTERED", "nameLocation": "1935:11:4", "nodeType": "VariableDeclaration", "scope": 797, "src": "1910:40:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 676, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1910:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 677, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1949:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "private"}, {"constant": true, "id": 681, "mutability": "constant", "name": "ENTERED", "nameLocation": "1981:7:4", "nodeType": "VariableDeclaration", "scope": 797, "src": "1956:36:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 679, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1956:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "32", "id": 680, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1991:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "visibility": "private"}, {"canonicalName": "ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "documentation": {"id": 682, "nodeType": "StructuredDocumentation", "src": "1999:73:4", "text": "@custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard"}, "id": 685, "members": [{"constant": false, "id": 684, "mutability": "mutable", "name": "_status", "nameLocation": "2125:7:4", "nodeType": "VariableDeclaration", "scope": 685, "src": "2117:15:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 683, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2117:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "ReentrancyGuardStorage", "nameLocation": "2084:22:4", "nodeType": "StructDefinition", "scope": 797, "src": "2077:62:4", "visibility": "public"}, {"constant": true, "id": 688, "mutability": "constant", "name": "ReentrancyGuardStorageLocation", "nameLocation": "2289:30:4", "nodeType": "VariableDeclaration", "scope": 797, "src": "2264:124:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 686, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2264:7:4", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"hexValue": "307839623737396231373432326430646639323232333031386233326234643166613436653037313732336436383137653234383664303033626563633535663030", "id": 687, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2322:66:4", "typeDescriptions": {"typeIdentifier": "t_rational_70319816728846589445362000750570655803700195216363692647688146666176345628416_by_1", "typeString": "int_const 7031...(69 digits omitted)...8416"}, "value": "0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00"}, "visibility": "private"}, {"body": {"id": 695, "nodeType": "Block", "src": "2489:89:4", "statements": [{"AST": {"nativeSrc": "2508:64:4", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2508:64:4", "statements": [{"nativeSrc": "2522:40:4", "nodeType": "YulAssignment", "src": "2522:40:4", "value": {"name": "ReentrancyGuardStorageLocation", "nativeSrc": "2532:30:4", "nodeType": "YulIdentifier", "src": "2532:30:4"}, "variableNames": [{"name": "$.slot", "nativeSrc": "2522:6:4", "nodeType": "YulIdentifier", "src": "2522:6:4"}]}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 692, "isOffset": false, "isSlot": true, "src": "2522:6:4", "suffix": "slot", "valueSize": 1}, {"declaration": 688, "isOffset": false, "isSlot": false, "src": "2532:30:4", "valueSize": 1}], "id": 694, "nodeType": "InlineAssembly", "src": "2499:73:4"}]}, "id": 696, "implemented": true, "kind": "function", "modifiers": [], "name": "_getReentrancyGuardStorage", "nameLocation": "2404:26:4", "nodeType": "FunctionDefinition", "parameters": {"id": 689, "nodeType": "ParameterList", "parameters": [], "src": "2430:2:4"}, "returnParameters": {"id": 693, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 692, "mutability": "mutable", "name": "$", "nameLocation": "2486:1:4", "nodeType": "VariableDeclaration", "scope": 696, "src": "2455:32:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}, "typeName": {"id": 691, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 690, "name": "ReentrancyGuardStorage", "nameLocations": ["2455:22:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 685, "src": "2455:22:4"}, "referencedDeclaration": 685, "src": "2455:22:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}}, "visibility": "internal"}], "src": "2454:34:4"}, "scope": 797, "src": "2395:183:4", "stateMutability": "pure", "virtual": false, "visibility": "private"}, {"documentation": {"id": 697, "nodeType": "StructuredDocumentation", "src": "2584:52:4", "text": " @dev Unauthorized reentrant call."}, "errorSelector": "3ee5aeb5", "id": 699, "name": "ReentrancyGuardReentrantCall", "nameLocation": "2647:28:4", "nodeType": "ErrorDefinition", "parameters": {"id": 698, "nodeType": "ParameterList", "parameters": [], "src": "2675:2:4"}, "src": "2641:37:4"}, {"body": {"id": 707, "nodeType": "Block", "src": "2744:51:4", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 704, "name": "__ReentrancyGuard_init_unchained", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 726, "src": "2754:32:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 705, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2754:34:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 706, "nodeType": "ExpressionStatement", "src": "2754:34:4"}]}, "id": 708, "implemented": true, "kind": "function", "modifiers": [{"id": 702, "kind": "modifierInvocation", "modifierName": {"id": 701, "name": "onlyInitializing", "nameLocations": ["2727:16:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "2727:16:4"}, "nodeType": "ModifierInvocation", "src": "2727:16:4"}], "name": "__ReentrancyGuard_init", "nameLocation": "2693:22:4", "nodeType": "FunctionDefinition", "parameters": {"id": 700, "nodeType": "ParameterList", "parameters": [], "src": "2715:2:4"}, "returnParameters": {"id": 703, "nodeType": "ParameterList", "parameters": [], "src": "2744:0:4"}, "scope": 797, "src": "2684:111:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 725, "nodeType": "Block", "src": "2871:113:4", "statements": [{"assignments": [715], "declarations": [{"constant": false, "id": 715, "mutability": "mutable", "name": "$", "nameLocation": "2912:1:4", "nodeType": "VariableDeclaration", "scope": 725, "src": "2881:32:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}, "typeName": {"id": 714, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 713, "name": "ReentrancyGuardStorage", "nameLocations": ["2881:22:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 685, "src": "2881:22:4"}, "referencedDeclaration": 685, "src": "2881:22:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}}, "visibility": "internal"}], "id": 718, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 716, "name": "_getReentrancyGuardStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 696, "src": "2916:26:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_ReentrancyGuardStorage_$685_storage_ptr_$", "typeString": "function () pure returns (struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer)"}}, "id": 717, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2916:28:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "2881:63:4"}, {"expression": {"id": 723, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 719, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 715, "src": "2954:1:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "id": 721, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2956:7:4", "memberName": "_status", "nodeType": "MemberAccess", "referencedDeclaration": 684, "src": "2954:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 722, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 678, "src": "2966:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2954:23:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 724, "nodeType": "ExpressionStatement", "src": "2954:23:4"}]}, "id": 726, "implemented": true, "kind": "function", "modifiers": [{"id": 711, "kind": "modifierInvocation", "modifierName": {"id": 710, "name": "onlyInitializing", "nameLocations": ["2854:16:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 357, "src": "2854:16:4"}, "nodeType": "ModifierInvocation", "src": "2854:16:4"}], "name": "__ReentrancyGuard_init_unchained", "nameLocation": "2810:32:4", "nodeType": "FunctionDefinition", "parameters": {"id": 709, "nodeType": "ParameterList", "parameters": [], "src": "2842:2:4"}, "returnParameters": {"id": 712, "nodeType": "ParameterList", "parameters": [], "src": "2871:0:4"}, "scope": 797, "src": "2801:183:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 736, "nodeType": "Block", "src": "3385:79:4", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 729, "name": "_nonReentrantBefore", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 762, "src": "3395:19:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 730, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3395:21:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 731, "nodeType": "ExpressionStatement", "src": "3395:21:4"}, {"id": 732, "nodeType": "PlaceholderStatement", "src": "3426:1:4"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 733, "name": "_nonReentrantAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 778, "src": "3437:18:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 734, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3437:20:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 735, "nodeType": "ExpressionStatement", "src": "3437:20:4"}]}, "documentation": {"id": 727, "nodeType": "StructuredDocumentation", "src": "2990:366:4", "text": " @dev Prevents a contract from calling itself, directly or indirectly.\n Calling a `nonReentrant` function from another `nonReentrant`\n function is not supported. It is possible to prevent this from happening\n by making the `nonReentrant` function external, and making it call a\n `private` function that does the actual work."}, "id": 737, "name": "nonReentrant", "nameLocation": "3370:12:4", "nodeType": "ModifierDefinition", "parameters": {"id": 728, "nodeType": "ParameterList", "parameters": [], "src": "3382:2:4"}, "src": "3361:103:4", "virtual": false, "visibility": "internal"}, {"body": {"id": 761, "nodeType": "Block", "src": "3509:345:4", "statements": [{"assignments": [742], "declarations": [{"constant": false, "id": 742, "mutability": "mutable", "name": "$", "nameLocation": "3550:1:4", "nodeType": "VariableDeclaration", "scope": 761, "src": "3519:32:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}, "typeName": {"id": 741, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 740, "name": "ReentrancyGuardStorage", "nameLocations": ["3519:22:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 685, "src": "3519:22:4"}, "referencedDeclaration": 685, "src": "3519:22:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}}, "visibility": "internal"}], "id": 745, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 743, "name": "_getReentrancyGuardStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 696, "src": "3554:26:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_ReentrancyGuardStorage_$685_storage_ptr_$", "typeString": "function () pure returns (struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer)"}}, "id": 744, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3554:28:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "3519:63:4"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 749, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 746, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "3670:1:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "id": 747, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3672:7:4", "memberName": "_status", "nodeType": "MemberAccess", "referencedDeclaration": 684, "src": "3670:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 748, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 681, "src": "3683:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3670:20:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 754, "nodeType": "IfStatement", "src": "3666:88:4", "trueBody": {"id": 753, "nodeType": "Block", "src": "3692:62:4", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 750, "name": "ReentrancyGuardReentrantCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 699, "src": "3713:28:4", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 751, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3713:30:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 752, "nodeType": "RevertStatement", "src": "3706:37:4"}]}}, {"expression": {"id": 759, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 755, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "3828:1:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "id": 757, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "3830:7:4", "memberName": "_status", "nodeType": "MemberAccess", "referencedDeclaration": 684, "src": "3828:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 758, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 681, "src": "3840:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3828:19:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 760, "nodeType": "ExpressionStatement", "src": "3828:19:4"}]}, "id": 762, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantBefore", "nameLocation": "3479:19:4", "nodeType": "FunctionDefinition", "parameters": {"id": 738, "nodeType": "ParameterList", "parameters": [], "src": "3498:2:4"}, "returnParameters": {"id": 739, "nodeType": "ParameterList", "parameters": [], "src": "3509:0:4"}, "scope": 797, "src": "3470:384:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 777, "nodeType": "Block", "src": "3898:245:4", "statements": [{"assignments": [767], "declarations": [{"constant": false, "id": 767, "mutability": "mutable", "name": "$", "nameLocation": "3939:1:4", "nodeType": "VariableDeclaration", "scope": 777, "src": "3908:32:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}, "typeName": {"id": 766, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 765, "name": "ReentrancyGuardStorage", "nameLocations": ["3908:22:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 685, "src": "3908:22:4"}, "referencedDeclaration": 685, "src": "3908:22:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}}, "visibility": "internal"}], "id": 770, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 768, "name": "_getReentrancyGuardStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 696, "src": "3943:26:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_ReentrancyGuardStorage_$685_storage_ptr_$", "typeString": "function () pure returns (struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer)"}}, "id": 769, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3943:28:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "3908:63:4"}, {"expression": {"id": 775, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 771, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 767, "src": "4113:1:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "id": 773, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4115:7:4", "memberName": "_status", "nodeType": "MemberAccess", "referencedDeclaration": 684, "src": "4113:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 774, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 678, "src": "4125:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4113:23:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 776, "nodeType": "ExpressionStatement", "src": "4113:23:4"}]}, "id": 778, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantAfter", "nameLocation": "3869:18:4", "nodeType": "FunctionDefinition", "parameters": {"id": 763, "nodeType": "ParameterList", "parameters": [], "src": "3887:2:4"}, "returnParameters": {"id": 764, "nodeType": "ParameterList", "parameters": [], "src": "3898:0:4"}, "scope": 797, "src": "3860:283:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 795, "nodeType": "Block", "src": "4386:117:4", "statements": [{"assignments": [786], "declarations": [{"constant": false, "id": 786, "mutability": "mutable", "name": "$", "nameLocation": "4427:1:4", "nodeType": "VariableDeclaration", "scope": 795, "src": "4396:32:4", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}, "typeName": {"id": 785, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 784, "name": "ReentrancyGuardStorage", "nameLocations": ["4396:22:4"], "nodeType": "IdentifierPath", "referencedDeclaration": 685, "src": "4396:22:4"}, "referencedDeclaration": 685, "src": "4396:22:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage"}}, "visibility": "internal"}], "id": 789, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 787, "name": "_getReentrancyGuardStorage", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 696, "src": "4431:26:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$__$returns$_t_struct$_ReentrancyGuardStorage_$685_storage_ptr_$", "typeString": "function () pure returns (struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer)"}}, "id": 788, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4431:28:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "nodeType": "VariableDeclarationStatement", "src": "4396:63:4"}, {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 793, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 790, "name": "$", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 786, "src": "4476:1:4", "typeDescriptions": {"typeIdentifier": "t_struct$_ReentrancyGuardStorage_$685_storage_ptr", "typeString": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage storage pointer"}}, "id": 791, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4478:7:4", "memberName": "_status", "nodeType": "MemberAccess", "referencedDeclaration": 684, "src": "4476:9:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 792, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 681, "src": "4489:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4476:20:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 783, "id": 794, "nodeType": "Return", "src": "4469:27:4"}]}, "documentation": {"id": 779, "nodeType": "StructuredDocumentation", "src": "4149:168:4", "text": " @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n `nonReentrant` function in the call stack."}, "id": 796, "implemented": true, "kind": "function", "modifiers": [], "name": "_reentrancyGuardEntered", "nameLocation": "4331:23:4", "nodeType": "FunctionDefinition", "parameters": {"id": 780, "nodeType": "ParameterList", "parameters": [], "src": "4354:2:4"}, "returnParameters": {"id": 783, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 782, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 796, "src": "4380:4:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 781, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4380:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "4379:6:4"}, "scope": 797, "src": "4322:181:4", "stateMutability": "view", "virtual": false, "visibility": "internal"}], "scope": 798, "src": "1094:3411:4", "usedErrors": [211, 214, 699], "usedEvents": [219]}], "src": "109:4397:4"}, "id": 4}, "contracts/PHRSDepositContract.sol": {"ast": {"absolutePath": "contracts/PHRSDepositContract.sol", "exportedSymbols": {"ContextUpgradeable": [508], "Initializable": [462], "OwnableUpgradeable": [194], "PHRSDepositContract": [1138], "PausableUpgradeable": [668], "ReentrancyGuardUpgradeable": [797]}, "id": 1139, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 799, "literals": ["solidity", "^", "0.8", ".22"], "nodeType": "PragmaDirective", "src": "32:24:5"}, {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "file": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol", "id": 800, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1139, "sourceUnit": 798, "src": "58:82:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "file": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "id": 801, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1139, "sourceUnit": 195, "src": "141:75:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol", "file": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol", "id": 802, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1139, "sourceUnit": 669, "src": "217:75:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "file": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "id": 803, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1139, "sourceUnit": 463, "src": "293:75:5", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 805, "name": "Initializable", "nameLocations": ["616:13:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 462, "src": "616:13:5"}, "id": 806, "nodeType": "InheritanceSpecifier", "src": "616:13:5"}, {"baseName": {"id": 807, "name": "ReentrancyGuardUpgradeable", "nameLocations": ["635:26:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 797, "src": "635:26:5"}, "id": 808, "nodeType": "InheritanceSpecifier", "src": "635:26:5"}, {"baseName": {"id": 809, "name": "OwnableUpgradeable", "nameLocations": ["667:18:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 194, "src": "667:18:5"}, "id": 810, "nodeType": "InheritanceSpecifier", "src": "667:18:5"}, {"baseName": {"id": 811, "name": "PausableUpgradeable", "nameLocations": ["691:19:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 668, "src": "691:19:5"}, "id": 812, "nodeType": "InheritanceSpecifier", "src": "691:19:5"}], "canonicalName": "PHRSDepositContract", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 804, "nodeType": "StructuredDocumentation", "src": "370:209:5", "text": " @title PHRS Deposit Contract\n @dev 用于处理PHRS原生币充值到游戏系统的可升级智能合约\n @notice 支持用户将PHRS原生币充值到游戏账户，发射事件供后端监听"}, "fullyImplemented": true, "id": 1138, "linearizedBaseContracts": [1138, 668, 194, 508, 797, 462], "name": "PHRSDepositContract", "nameLocation": "589:19:5", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "functionSelector": "645006ca", "id": 814, "mutability": "mutable", "name": "minDepositAmount", "nameLocation": "758:16:5", "nodeType": "VariableDeclaration", "scope": 1138, "src": "743:31:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 813, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "743:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"constant": false, "functionSelector": "8ed83271", "id": 816, "mutability": "mutable", "name": "maxDepositAmount", "nameLocation": "822:16:5", "nodeType": "VariableDeclaration", "scope": 1138, "src": "807:31:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 815, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "807:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "90890809c654f11d6e72a28fa60149770a0d11ec6c92319d6ceb2bb0a4ea1a15", "id": 824, "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "871:7:5", "nodeType": "EventDefinition", "parameters": {"id": 823, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 818, "indexed": true, "mutability": "mutable", "name": "user", "nameLocation": "904:4:5", "nodeType": "VariableDeclaration", "scope": 824, "src": "888:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 817, "name": "address", "nodeType": "ElementaryTypeName", "src": "888:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 820, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "926:6:5", "nodeType": "VariableDeclaration", "scope": 824, "src": "918:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 819, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "918:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 822, "indexed": false, "mutability": "mutable", "name": "timestamp", "nameLocation": "950:9:5", "nodeType": "VariableDeclaration", "scope": 824, "src": "942:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 821, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "942:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "878:87:5"}, "src": "865:101:5"}, {"anonymous": false, "eventSelector": "5fb4589fcdfab8bd40d9776abc10876bb1cb02c0edab28d05cc42869b40e0329", "id": 830, "name": "MinDepositAmountUpdated", "nameLocation": "978:23:5", "nodeType": "EventDefinition", "parameters": {"id": 829, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 826, "indexed": false, "mutability": "mutable", "name": "oldAmount", "nameLocation": "1010:9:5", "nodeType": "VariableDeclaration", "scope": 830, "src": "1002:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 825, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1002:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 828, "indexed": false, "mutability": "mutable", "name": "newAmount", "nameLocation": "1029:9:5", "nodeType": "VariableDeclaration", "scope": 830, "src": "1021:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 827, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1021:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1001:38:5"}, "src": "972:68:5"}, {"anonymous": false, "eventSelector": "2bb9e3fc7b14157e1812fdbe23016f0756b7ebb061c80589dec1550779776c78", "id": 836, "name": "MaxDepositAmountUpdated", "nameLocation": "1051:23:5", "nodeType": "EventDefinition", "parameters": {"id": 835, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 832, "indexed": false, "mutability": "mutable", "name": "oldAmount", "nameLocation": "1083:9:5", "nodeType": "VariableDeclaration", "scope": 836, "src": "1075:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 831, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1075:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 834, "indexed": false, "mutability": "mutable", "name": "newAmount", "nameLocation": "1102:9:5", "nodeType": "VariableDeclaration", "scope": 836, "src": "1094:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 833, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1094:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1074:38:5"}, "src": "1045:68:5"}, {"anonymous": false, "eventSelector": "bb43d2e6a13f52d71a3a836cf197fa240fb4ef40914b6e13cc966fd6dfb19b68", "id": 842, "name": "EmergencyWithdraw", "nameLocation": "1124:17:5", "nodeType": "EventDefinition", "parameters": {"id": 841, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 838, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "1150:6:5", "nodeType": "VariableDeclaration", "scope": 842, "src": "1142:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 837, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1142:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 840, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "1174:2:5", "nodeType": "VariableDeclaration", "scope": 842, "src": "1158:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 839, "name": "address", "nodeType": "ElementaryTypeName", "src": "1158:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1141:36:5"}, "src": "1118:60:5"}, {"errorSelector": "2c5211c6", "id": 844, "name": "InvalidAmount", "nameLocation": "1210:13:5", "nodeType": "ErrorDefinition", "parameters": {"id": 843, "nodeType": "ParameterList", "parameters": [], "src": "1223:2:5"}, "src": "1204:22:5"}, {"errorSelector": "c2f5625a", "id": 846, "name": "AmountTooSmall", "nameLocation": "1237:14:5", "nodeType": "ErrorDefinition", "parameters": {"id": 845, "nodeType": "ParameterList", "parameters": [], "src": "1251:2:5"}, "src": "1231:23:5"}, {"errorSelector": "06250401", "id": 848, "name": "Amount<PERSON>ooLarge", "nameLocation": "1265:14:5", "nodeType": "ErrorDefinition", "parameters": {"id": 847, "nodeType": "ParameterList", "parameters": [], "src": "1279:2:5"}, "src": "1259:23:5"}, {"errorSelector": "90b8ec18", "id": 850, "name": "TransferFailed", "nameLocation": "1293:14:5", "nodeType": "ErrorDefinition", "parameters": {"id": 849, "nodeType": "ParameterList", "parameters": [], "src": "1307:2:5"}, "src": "1287:23:5"}, {"errorSelector": "e6c4247b", "id": 852, "name": "InvalidAddress", "nameLocation": "1321:14:5", "nodeType": "ErrorDefinition", "parameters": {"id": 851, "nodeType": "ParameterList", "parameters": [], "src": "1335:2:5"}, "src": "1315:23:5"}, {"body": {"id": 859, "nodeType": "Block", "src": "1411:39:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 856, "name": "_disableInitializers", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 416, "src": "1421:20:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 857, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1421:22:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 858, "nodeType": "ExpressionStatement", "src": "1421:22:5"}]}, "documentation": {"id": 853, "nodeType": "StructuredDocumentation", "src": "1344:48:5", "text": "@custom:oz-upgrades-unsafe-allow constructor"}, "id": 860, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 854, "nodeType": "ParameterList", "parameters": [], "src": "1408:2:5"}, "returnParameters": {"id": 855, "nodeType": "ParameterList", "parameters": [], "src": "1411:0:5"}, "scope": 1138, "src": "1397:53:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 903, "nodeType": "Block", "src": "1738:334:5", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 872, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 870, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 863, "src": "1752:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 871, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1773:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1752:22:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 876, "nodeType": "IfStatement", "src": "1748:50:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 873, "name": "InvalidAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 844, "src": "1783:13:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 874, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1783:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 875, "nodeType": "RevertStatement", "src": "1776:22:5"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 877, "name": "_maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 865, "src": "1812:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 878, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 863, "src": "1833:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1812:38:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 883, "nodeType": "IfStatement", "src": "1808:66:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 880, "name": "InvalidAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 844, "src": "1859:13:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 881, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1859:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 882, "nodeType": "RevertStatement", "src": "1852:22:5"}}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 884, "name": "__ReentrancyGuard_init", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "1885:22:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 885, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1885:24:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 886, "nodeType": "ExpressionStatement", "src": "1885:24:5"}, {"expression": {"arguments": [{"expression": {"id": 888, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1934:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 889, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1938:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "1934:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 887, "name": "__Ownable_init", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 54, "src": "1919:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 890, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1919:26:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 891, "nodeType": "ExpressionStatement", "src": "1919:26:5"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 892, "name": "__Pausable_init", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 572, "src": "1955:15:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 893, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1955:17:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 894, "nodeType": "ExpressionStatement", "src": "1955:17:5"}, {"expression": {"id": 897, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 895, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "1983:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 896, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 863, "src": "2002:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1983:36:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 898, "nodeType": "ExpressionStatement", "src": "1983:36:5"}, {"expression": {"id": 901, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 899, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "2029:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 900, "name": "_maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 865, "src": "2048:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2029:36:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 902, "nodeType": "ExpressionStatement", "src": "2029:36:5"}]}, "documentation": {"id": 861, "nodeType": "StructuredDocumentation", "src": "1456:162:5", "text": " @dev 初始化函数，替代构造函数\n @param _minDepositAmount 最小充值金额\n @param _maxDepositAmount 最大充值金额"}, "functionSelector": "e4a30116", "id": 904, "implemented": true, "kind": "function", "modifiers": [{"id": 868, "kind": "modifierInvocation", "modifierName": {"id": 867, "name": "initializer", "nameLocations": ["1726:11:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 302, "src": "1726:11:5"}, "nodeType": "ModifierInvocation", "src": "1726:11:5"}], "name": "initialize", "nameLocation": "1632:10:5", "nodeType": "FunctionDefinition", "parameters": {"id": 866, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 863, "mutability": "mutable", "name": "_minDepositAmount", "nameLocation": "1660:17:5", "nodeType": "VariableDeclaration", "scope": 904, "src": "1652:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 862, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1652:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 865, "mutability": "mutable", "name": "_maxDepositAmount", "nameLocation": "1695:17:5", "nodeType": "VariableDeclaration", "scope": 904, "src": "1687:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 864, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1687:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1642:76:5"}, "returnParameters": {"id": 869, "nodeType": "ParameterList", "parameters": [], "src": "1738:0:5"}, "scope": 1138, "src": "1623:449:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 915, "nodeType": "Block", "src": "2254:34:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 912, "name": "_processDeposit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 967, "src": "2264:15:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 913, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2264:17:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 914, "nodeType": "ExpressionStatement", "src": "2264:17:5"}]}, "documentation": {"id": 905, "nodeType": "StructuredDocumentation", "src": "2078:108:5", "text": " @dev 充值PHRS原生币\n @notice 用户发送PHRS原生币到此函数进行充值"}, "functionSelector": "d0e30db0", "id": 916, "implemented": true, "kind": "function", "modifiers": [{"id": 908, "kind": "modifierInvocation", "modifierName": {"id": 907, "name": "nonReentrant", "nameLocations": ["2227:12:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 737, "src": "2227:12:5"}, "nodeType": "ModifierInvocation", "src": "2227:12:5"}, {"id": 910, "kind": "modifierInvocation", "modifierName": {"id": 909, "name": "whenNotPaused", "nameLocations": ["2240:13:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 558, "src": "2240:13:5"}, "nodeType": "ModifierInvocation", "src": "2240:13:5"}], "name": "deposit", "nameLocation": "2200:7:5", "nodeType": "FunctionDefinition", "parameters": {"id": 906, "nodeType": "ParameterList", "parameters": [], "src": "2207:2:5"}, "returnParameters": {"id": 911, "nodeType": "ParameterList", "parameters": [], "src": "2254:0:5"}, "scope": 1138, "src": "2191:97:5", "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"body": {"id": 923, "nodeType": "Block", "src": "2380:34:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 920, "name": "_processDeposit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 967, "src": "2390:15:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 921, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2390:17:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 922, "nodeType": "ExpressionStatement", "src": "2390:17:5"}]}, "documentation": {"id": 917, "nodeType": "StructuredDocumentation", "src": "2294:54:5", "text": " @dev 接收原生币的回退函数"}, "id": 924, "implemented": true, "kind": "receive", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 918, "nodeType": "ParameterList", "parameters": [], "src": "2360:2:5"}, "returnParameters": {"id": 919, "nodeType": "ParameterList", "parameters": [], "src": "2380:0:5"}, "scope": 1138, "src": "2353:61:5", "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"body": {"id": 966, "nodeType": "Block", "src": "2509:356:5", "statements": [{"assignments": [929], "declarations": [{"constant": false, "id": 929, "mutability": "mutable", "name": "amount", "nameLocation": "2527:6:5", "nodeType": "VariableDeclaration", "scope": 966, "src": "2519:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 928, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2519:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 932, "initialValue": {"expression": {"id": 930, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2536:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 931, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2540:5:5", "memberName": "value", "nodeType": "MemberAccess", "src": "2536:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2519:26:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 935, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 933, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 929, "src": "2560:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 934, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2570:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2560:11:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 939, "nodeType": "IfStatement", "src": "2556:39:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 936, "name": "InvalidAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 844, "src": "2580:13:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 937, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2580:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 938, "nodeType": "RevertStatement", "src": "2573:22:5"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 942, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 940, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 929, "src": "2609:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 941, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "2618:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2609:25:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 946, "nodeType": "IfStatement", "src": "2605:54:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 943, "name": "AmountTooSmall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 846, "src": "2643:14:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 944, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2643:16:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 945, "nodeType": "RevertStatement", "src": "2636:23:5"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 949, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 947, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 929, "src": "2673:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 948, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "2682:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2673:25:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 953, "nodeType": "IfStatement", "src": "2669:54:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 950, "name": "Amount<PERSON>ooLarge", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 848, "src": "2707:14:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 951, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2707:16:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 952, "nodeType": "RevertStatement", "src": "2700:23:5"}}, {"assignments": [955], "declarations": [{"constant": false, "id": 955, "mutability": "mutable", "name": "user", "nameLocation": "2742:4:5", "nodeType": "VariableDeclaration", "scope": 966, "src": "2734:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 954, "name": "address", "nodeType": "ElementaryTypeName", "src": "2734:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 958, "initialValue": {"expression": {"id": 956, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2749:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 957, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2753:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "2749:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2734:25:5"}, {"eventCall": {"arguments": [{"id": 960, "name": "user", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 955, "src": "2828:4:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 961, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 929, "src": "2834:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 962, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "2842:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 963, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2848:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "2842:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 959, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 824, "src": "2820:7:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (address,uint256,uint256)"}}, "id": 964, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2820:38:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 965, "nodeType": "EmitStatement", "src": "2815:43:5"}]}, "documentation": {"id": 925, "nodeType": "StructuredDocumentation", "src": "2420:48:5", "text": " @dev 内部充值处理函数"}, "id": 967, "implemented": true, "kind": "function", "modifiers": [], "name": "_processDeposit", "nameLocation": "2482:15:5", "nodeType": "FunctionDefinition", "parameters": {"id": 926, "nodeType": "ParameterList", "parameters": [], "src": "2497:2:5"}, "returnParameters": {"id": 927, "nodeType": "ParameterList", "parameters": [], "src": "2509:0:5"}, "scope": 1138, "src": "2473:392:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 1002, "nodeType": "Block", "src": "3076:356:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 978, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 976, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 970, "src": "3094:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 977, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3114:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3094:21:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416d6f756e74206d7573742062652067726561746572207468616e2030", "id": 979, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3117:31:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3e76f273c719bb7d23db533a2dc9a822ae7d899fcd42eb8910272e24764e8296", "typeString": "literal_string \"Amount must be greater than 0\""}, "value": "Amount must be greater than 0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3e76f273c719bb7d23db533a2dc9a822ae7d899fcd42eb8910272e24764e8296", "typeString": "literal_string \"Amount must be greater than 0\""}], "id": 975, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3086:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 980, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3086:63:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 981, "nodeType": "ExpressionStatement", "src": "3086:63:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 985, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 983, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 970, "src": "3167:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 984, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "3187:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3167:36:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4d696e20616d6f756e74206d757374206265206c657373207468616e206d617820616d6f756e74", "id": 986, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3205:41:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_446e99ffdf2e03d3177d40dd993d55d45602fea049771b76ceb15b1e4a0f2a97", "typeString": "literal_string \"Min amount must be less than max amount\""}, "value": "Min amount must be less than max amount"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_446e99ffdf2e03d3177d40dd993d55d45602fea049771b76ceb15b1e4a0f2a97", "typeString": "literal_string \"Min amount must be less than max amount\""}], "id": 982, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3159:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 987, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3159:88:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 988, "nodeType": "ExpressionStatement", "src": "3159:88:5"}, {"assignments": [990], "declarations": [{"constant": false, "id": 990, "mutability": "mutable", "name": "oldAmount", "nameLocation": "3274:9:5", "nodeType": "VariableDeclaration", "scope": 1002, "src": "3266:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 989, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3266:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 992, "initialValue": {"id": 991, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "3286:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3266:36:5"}, {"expression": {"id": 995, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 993, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "3312:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 994, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 970, "src": "3331:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3312:36:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 996, "nodeType": "ExpressionStatement", "src": "3312:36:5"}, {"eventCall": {"arguments": [{"id": 998, "name": "oldAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 990, "src": "3396:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 999, "name": "_minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 970, "src": "3407:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 997, "name": "MinDepositAmountUpdated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 830, "src": "3372:23:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 1000, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3372:53:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1001, "nodeType": "EmitStatement", "src": "3367:58:5"}]}, "documentation": {"id": 968, "nodeType": "StructuredDocumentation", "src": "2873:123:5", "text": " @dev 设置最小充值金额（仅管理员）\n @param _minDepositAmount 新的最小充值金额"}, "functionSelector": "2a80cda3", "id": 1003, "implemented": true, "kind": "function", "modifiers": [{"id": 973, "kind": "modifierInvocation", "modifierName": {"id": 972, "name": "only<PERSON><PERSON>er", "nameLocations": ["3066:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "3066:9:5"}, "nodeType": "ModifierInvocation", "src": "3066:9:5"}], "name": "setMinDepositAmount", "nameLocation": "3010:19:5", "nodeType": "FunctionDefinition", "parameters": {"id": 971, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 970, "mutability": "mutable", "name": "_minDepositAmount", "nameLocation": "3038:17:5", "nodeType": "VariableDeclaration", "scope": 1003, "src": "3030:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 969, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3030:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3029:27:5"}, "returnParameters": {"id": 974, "nodeType": "ParameterList", "parameters": [], "src": "3076:0:5"}, "scope": 1138, "src": "3001:431:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1031, "nodeType": "Block", "src": "3641:286:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1014, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1012, "name": "_maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1006, "src": "3659:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 1013, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "3679:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3659:36:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4d617820616d6f756e74206d7573742062652067726561746572207468616e206d696e20616d6f756e74", "id": 1015, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3697:44:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_caf27fc83f7a2f76ea0e23c346f21e48964232371e524260eef1e330d3edebe4", "typeString": "literal_string \"Max amount must be greater than min amount\""}, "value": "Max amount must be greater than min amount"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_caf27fc83f7a2f76ea0e23c346f21e48964232371e524260eef1e330d3edebe4", "typeString": "literal_string \"Max amount must be greater than min amount\""}], "id": 1011, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3651:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1016, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3651:91:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1017, "nodeType": "ExpressionStatement", "src": "3651:91:5"}, {"assignments": [1019], "declarations": [{"constant": false, "id": 1019, "mutability": "mutable", "name": "oldAmount", "nameLocation": "3769:9:5", "nodeType": "VariableDeclaration", "scope": 1031, "src": "3761:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1018, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3761:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1021, "initialValue": {"id": 1020, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "3781:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3761:36:5"}, {"expression": {"id": 1024, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1022, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "3807:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1023, "name": "_maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1006, "src": "3826:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3807:36:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1025, "nodeType": "ExpressionStatement", "src": "3807:36:5"}, {"eventCall": {"arguments": [{"id": 1027, "name": "oldAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1019, "src": "3891:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 1028, "name": "_maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1006, "src": "3902:17:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 1026, "name": "MaxDepositAmountUpdated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 836, "src": "3867:23:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 1029, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3867:53:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1030, "nodeType": "EmitStatement", "src": "3862:58:5"}]}, "documentation": {"id": 1004, "nodeType": "StructuredDocumentation", "src": "3438:123:5", "text": " @dev 设置最大充值金额（仅管理员）\n @param _maxDepositAmount 新的最大充值金额"}, "functionSelector": "c4511c6a", "id": 1032, "implemented": true, "kind": "function", "modifiers": [{"id": 1009, "kind": "modifierInvocation", "modifierName": {"id": 1008, "name": "only<PERSON><PERSON>er", "nameLocations": ["3631:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "3631:9:5"}, "nodeType": "ModifierInvocation", "src": "3631:9:5"}], "name": "setMaxDepositAmount", "nameLocation": "3575:19:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1007, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1006, "mutability": "mutable", "name": "_maxDepositAmount", "nameLocation": "3603:17:5", "nodeType": "VariableDeclaration", "scope": 1032, "src": "3595:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1005, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3595:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3594:27:5"}, "returnParameters": {"id": 1010, "nodeType": "ParameterList", "parameters": [], "src": "3641:0:5"}, "scope": 1138, "src": "3566:361:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1041, "nodeType": "Block", "src": "4028:25:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 1038, "name": "_pause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 643, "src": "4038:6:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 1039, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4038:8:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1040, "nodeType": "ExpressionStatement", "src": "4038:8:5"}]}, "documentation": {"id": 1033, "nodeType": "StructuredDocumentation", "src": "3933:54:5", "text": " @dev 暂停合约（仅管理员）"}, "functionSelector": "8456cb59", "id": 1042, "implemented": true, "kind": "function", "modifiers": [{"id": 1036, "kind": "modifierInvocation", "modifierName": {"id": 1035, "name": "only<PERSON><PERSON>er", "nameLocations": ["4018:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "4018:9:5"}, "nodeType": "ModifierInvocation", "src": "4018:9:5"}], "name": "pause", "nameLocation": "4001:5:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1034, "nodeType": "ParameterList", "parameters": [], "src": "4006:2:5"}, "returnParameters": {"id": 1037, "nodeType": "ParameterList", "parameters": [], "src": "4028:0:5"}, "scope": 1138, "src": "3992:61:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1051, "nodeType": "Block", "src": "4156:27:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 1048, "name": "_unpause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 667, "src": "4166:8:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 1049, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4166:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1050, "nodeType": "ExpressionStatement", "src": "4166:10:5"}]}, "documentation": {"id": 1043, "nodeType": "StructuredDocumentation", "src": "4059:54:5", "text": " @dev 恢复合约（仅管理员）"}, "functionSelector": "3f4ba83a", "id": 1052, "implemented": true, "kind": "function", "modifiers": [{"id": 1046, "kind": "modifierInvocation", "modifierName": {"id": 1045, "name": "only<PERSON><PERSON>er", "nameLocations": ["4146:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "4146:9:5"}, "nodeType": "ModifierInvocation", "src": "4146:9:5"}], "name": "unpause", "nameLocation": "4127:7:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1044, "nodeType": "ParameterList", "parameters": [], "src": "4134:2:5"}, "returnParameters": {"id": 1047, "nodeType": "ParameterList", "parameters": [], "src": "4156:0:5"}, "scope": 1138, "src": "4118:65:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1103, "nodeType": "Block", "src": "4425:278:5", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 1067, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1062, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1057, "src": "4439:2:5", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 1065, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4453:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 1064, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4445:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1063, "name": "address", "nodeType": "ElementaryTypeName", "src": "4445:7:5", "typeDescriptions": {}}}, "id": 1066, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4445:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "4439:16:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1071, "nodeType": "IfStatement", "src": "4435:45:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 1068, "name": "InvalidAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 852, "src": "4464:14:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 1069, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4464:16:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1070, "nodeType": "RevertStatement", "src": "4457:23:5"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1078, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1072, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1055, "src": "4494:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"expression": {"arguments": [{"id": 1075, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "4511:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}], "id": 1074, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4503:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1073, "name": "address", "nodeType": "ElementaryTypeName", "src": "4503:7:5", "typeDescriptions": {}}}, "id": 1076, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4503:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1077, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4517:7:5", "memberName": "balance", "nodeType": "MemberAccess", "src": "4503:21:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4494:30:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1082, "nodeType": "IfStatement", "src": "4490:58:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 1079, "name": "InvalidAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 844, "src": "4533:13:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 1080, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4533:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1081, "nodeType": "RevertStatement", "src": "4526:22:5"}}, {"assignments": [1084, null], "declarations": [{"constant": false, "id": 1084, "mutability": "mutable", "name": "success", "nameLocation": "4565:7:5", "nodeType": "VariableDeclaration", "scope": 1103, "src": "4560:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1083, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4560:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, null], "id": 1091, "initialValue": {"arguments": [{"hexValue": "", "id": 1089, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4601:2:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"id": 1085, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1057, "src": "4578:2:5", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 1086, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4581:4:5", "memberName": "call", "nodeType": "MemberAccess", "src": "4578:7:5", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 1088, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"id": 1087, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1055, "src": "4593:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "4578:22:5", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$value", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 1090, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4578:26:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "4559:45:5"}, {"condition": {"id": 1093, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4618:8:5", "subExpression": {"id": 1092, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1084, "src": "4619:7:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1097, "nodeType": "IfStatement", "src": "4614:37:5", "trueBody": {"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 1094, "name": "TransferFailed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 850, "src": "4635:14:5", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 1095, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4635:16:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1096, "nodeType": "RevertStatement", "src": "4628:23:5"}}, {"eventCall": {"arguments": [{"id": 1099, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1055, "src": "4685:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 1100, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1057, "src": "4693:2:5", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address_payable", "typeString": "address payable"}], "id": 1098, "name": "EmergencyWithdraw", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 842, "src": "4667:17:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_address_$returns$__$", "typeString": "function (uint256,address)"}}, "id": 1101, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4667:29:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1102, "nodeType": "EmitStatement", "src": "4662:34:5"}]}, "documentation": {"id": 1053, "nodeType": "StructuredDocumentation", "src": "4189:127:5", "text": " @dev 紧急提取原生币（仅管理员）\n @param amount 提取金额\n @param to 接收地址"}, "functionSelector": "2f940c70", "id": 1104, "implemented": true, "kind": "function", "modifiers": [{"id": 1060, "kind": "modifierInvocation", "modifierName": {"id": 1059, "name": "only<PERSON><PERSON>er", "nameLocations": ["4415:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 89, "src": "4415:9:5"}, "nodeType": "ModifierInvocation", "src": "4415:9:5"}], "name": "emergencyWithdraw", "nameLocation": "4330:17:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1058, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1055, "mutability": "mutable", "name": "amount", "nameLocation": "4365:6:5", "nodeType": "VariableDeclaration", "scope": 1104, "src": "4357:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1054, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4357:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1057, "mutability": "mutable", "name": "to", "nameLocation": "4397:2:5", "nodeType": "VariableDeclaration", "scope": 1104, "src": "4381:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}, "typeName": {"id": 1056, "name": "address", "nodeType": "ElementaryTypeName", "src": "4381:15:5", "stateMutability": "payable", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}], "src": "4347:58:5"}, "returnParameters": {"id": 1061, "nodeType": "ParameterList", "parameters": [], "src": "4425:0:5"}, "scope": 1138, "src": "4321:382:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1116, "nodeType": "Block", "src": "4856:45:5", "statements": [{"expression": {"expression": {"arguments": [{"id": 1112, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "4881:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}], "id": 1111, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4873:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1110, "name": "address", "nodeType": "ElementaryTypeName", "src": "4873:7:5", "typeDescriptions": {}}}, "id": 1113, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4873:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1114, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4887:7:5", "memberName": "balance", "nodeType": "MemberAccess", "src": "4873:21:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1109, "id": 1115, "nodeType": "Return", "src": "4866:28:5"}]}, "documentation": {"id": 1105, "nodeType": "StructuredDocumentation", "src": "4709:88:5", "text": " @dev 获取合约原生币余额\n @return 合约原生币余额"}, "functionSelector": "12065fe0", "id": 1117, "implemented": true, "kind": "function", "modifiers": [], "name": "getBalance", "nameLocation": "4811:10:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1106, "nodeType": "ParameterList", "parameters": [], "src": "4821:2:5"}, "returnParameters": {"id": 1109, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1108, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1117, "src": "4847:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1107, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4847:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4846:9:5"}, "scope": 1138, "src": "4802:99:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1136, "nodeType": "Block", "src": "5244:129:5", "statements": [{"expression": {"components": [{"id": 1127, "name": "minDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 814, "src": "5275:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 1128, "name": "maxDepositAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 816, "src": "5305:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"arguments": [{"id": 1131, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "5343:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_PHRSDepositContract_$1138", "typeString": "contract PHRSDepositContract"}], "id": 1130, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5335:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1129, "name": "address", "nodeType": "ElementaryTypeName", "src": "5335:7:5", "typeDescriptions": {}}}, "id": 1132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5335:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1133, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5349:7:5", "memberName": "balance", "nodeType": "MemberAccess", "src": "5335:21:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 1134, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "5261:105:5", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint256_$_t_uint256_$_t_uint256_$", "typeString": "tuple(uint256,uint256,uint256)"}}, "functionReturnParameters": 1126, "id": 1135, "nodeType": "Return", "src": "5254:112:5"}]}, "documentation": {"id": 1118, "nodeType": "StructuredDocumentation", "src": "4907:189:5", "text": " @dev 获取合约基本信息\n @return minAmount 最小充值金额\n @return maxAmount 最大充值金额\n @return contractBalance 合约原生币余额"}, "functionSelector": "7cc1f867", "id": 1137, "implemented": true, "kind": "function", "modifiers": [], "name": "getContractInfo", "nameLocation": "5110:15:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1119, "nodeType": "ParameterList", "parameters": [], "src": "5125:2:5"}, "returnParameters": {"id": 1126, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1121, "mutability": "mutable", "name": "minAmount", "nameLocation": "5168:9:5", "nodeType": "VariableDeclaration", "scope": 1137, "src": "5160:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1120, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5160:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1123, "mutability": "mutable", "name": "maxAmount", "nameLocation": "5195:9:5", "nodeType": "VariableDeclaration", "scope": 1137, "src": "5187:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1122, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5187:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1125, "mutability": "mutable", "name": "contractBalance", "nameLocation": "5222:15:5", "nodeType": "VariableDeclaration", "scope": 1137, "src": "5214:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1124, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5214:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5150:93:5"}, "scope": 1138, "src": "5101:272:5", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 1139, "src": "580:4795:5", "usedErrors": [30, 35, 211, 214, 547, 550, 699, 844, 846, 848, 850, 852], "usedEvents": [41, 219, 539, 544, 824, 830, 836, 842]}], "src": "32:5344:5"}, "id": 5}}, "contracts": {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol": {"OwnableUpgradeable": {"abi": [{"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. The initial owner is set to the address provided by the deployer. This can later be changed with {transferOwnership}. This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.\",\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":\"OwnableUpgradeable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"Initializable": {"abi": [{"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"}],\"devdoc\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor constructor() {     _disableInitializers(); } ``` ====\",\"details\":\"This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer function so it can only be called once. The {initializer} modifier provided by this contract will have this effect. The initialization functions use a version number. Once a version number is used, it is consumed and cannot be reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in case an upgrade adds a module that needs to be initialized. For example: [.hljs-theme-light.nopadding] ```solidity contract MyToken is ERC20Upgradeable {     function initialize() initializer public {         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");     } } contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {     function initializeV2() reinitializer(2) public {         __ERC20Permit_init(\\\"MyToken\\\");     } } ``` TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}. CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure that all initializers are idempotent. This is not verified automatically as constructors are by Solidity. [CAUTION] ==== Avoid leaving a contract uninitialized. An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke the {_disableInitializers} function in the constructor to automatically lock it when it is deployed: [.hljs-theme-light.nopadding] ```\",\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":\"Initializable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"ContextUpgradeable": {"abi": [{"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"}],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":\"ContextUpgradeable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol": {"PausableUpgradeable": {"abi": [{"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"paused()": "5c975abb"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which allows children to implement an emergency stop mechanism that can be triggered by an authorized account. This module is used through inheritance. It will make available the modifiers `whenNotPaused` and `whenPaused`, which can be applied to the functions of your contract. Note that they will not be pausable by simply including this module, only once the modifiers are put in place.\",\"errors\":{\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol\":\"PausableUpgradeable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol\":{\"keccak256\":\"0xa6bf6b7efe0e6625a9dcd30c5ddf52c4c24fe8372f37c7de9dbf5034746768d5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c353ee3705bbf6fadb84c0fb10ef1b736e8ca3ca1867814349d1487ed207beb\",\"dweb:/ipfs/QmcugaPssrzGGE8q4YZKm2ZhnD3kCijjcgdWWg76nWt3FY\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol": {"ReentrancyGuardUpgradeable": {"abi": [{"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"}],\"devdoc\":{\"details\":\"Contract module that helps prevent reentrant calls to a function. Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier available, which can be applied to functions to make sure there are no nested (reentrant) calls to them. Note that because there is a single `nonReentrant` guard, functions marked as `nonReentrant` may not call one another. This can be worked around by making those functions `private`, and then adding `external` `nonReentrant` entry points to them. TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at, consider using {ReentrancyGuardTransient} instead. TIP: If you would like to learn more about reentrancy and alternative ways to protect against it, check out our blog post https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\",\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":\"ReentrancyGuardUpgradeable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "contracts/PHRSDepositContract.sol": {"PHRSDepositContract": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "evm": {"bytecode": {"functionDebugData": {"@_860": {"entryPoint": null, "id": 860, "parameterSlots": 0, "returnSlots": 0}, "@_disableInitializers_416": {"entryPoint": 30, "id": 416, "parameterSlots": 0, "returnSlots": 0}, "@_getInitializableStorage_461": {"entryPoint": null, "id": 461, "parameterSlots": 0, "returnSlots": 1}, "@_initializableStorageSlot_447": {"entryPoint": null, "id": 447, "parameterSlots": 0, "returnSlots": 1}, "abi_encode_tuple_t_uint64__to_t_uint64__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}}, "generatedSources": [{"ast": {"nativeSrc": "0:216:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:216:6", "statements": [{"nativeSrc": "6:3:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:6", "statements": []}, {"body": {"nativeSrc": "113:101:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "113:101:6", "statements": [{"nativeSrc": "123:26:6", "nodeType": "YulAssignment", "src": "123:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "135:9:6", "nodeType": "YulIdentifier", "src": "135:9:6"}, {"kind": "number", "nativeSrc": "146:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "146:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "131:3:6", "nodeType": "YulIdentifier", "src": "131:3:6"}, "nativeSrc": "131:18:6", "nodeType": "YulFunctionCall", "src": "131:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "123:4:6", "nodeType": "YulIdentifier", "src": "123:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "165:9:6", "nodeType": "YulIdentifier", "src": "165:9:6"}, {"arguments": [{"name": "value0", "nativeSrc": "180:6:6", "nodeType": "YulIdentifier", "src": "180:6:6"}, {"arguments": [{"arguments": [{"kind": "number", "nativeSrc": "196:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "196:2:6", "type": "", "value": "64"}, {"kind": "number", "nativeSrc": "200:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "200:1:6", "type": "", "value": "1"}], "functionName": {"name": "shl", "nativeSrc": "192:3:6", "nodeType": "YulIdentifier", "src": "192:3:6"}, "nativeSrc": "192:10:6", "nodeType": "YulFunctionCall", "src": "192:10:6"}, {"kind": "number", "nativeSrc": "204:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "204:1:6", "type": "", "value": "1"}], "functionName": {"name": "sub", "nativeSrc": "188:3:6", "nodeType": "YulIdentifier", "src": "188:3:6"}, "nativeSrc": "188:18:6", "nodeType": "YulFunctionCall", "src": "188:18:6"}], "functionName": {"name": "and", "nativeSrc": "176:3:6", "nodeType": "YulIdentifier", "src": "176:3:6"}, "nativeSrc": "176:31:6", "nodeType": "YulFunctionCall", "src": "176:31:6"}], "functionName": {"name": "mstore", "nativeSrc": "158:6:6", "nodeType": "YulIdentifier", "src": "158:6:6"}, "nativeSrc": "158:50:6", "nodeType": "YulFunctionCall", "src": "158:50:6"}, "nativeSrc": "158:50:6", "nodeType": "YulExpressionStatement", "src": "158:50:6"}]}, "name": "abi_encode_tuple_t_uint64__to_t_uint64__fromStack_reversed", "nativeSrc": "14:200:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "82:9:6", "nodeType": "YulTypedName", "src": "82:9:6", "type": ""}, {"name": "value0", "nativeSrc": "93:6:6", "nodeType": "YulTypedName", "src": "93:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "104:4:6", "nodeType": "YulTypedName", "src": "104:4:6", "type": ""}], "src": "14:200:6"}]}, "contents": "{\n    { }\n    function abi_encode_tuple_t_uint64__to_t_uint64__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, and(value0, sub(shl(64, 1), 1)))\n    }\n}", "id": 6, "language": "<PERSON>l", "name": "#utility.yul"}], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x19 PUSH2 0x1E JUMP JUMPDEST PUSH2 0xD0 JUMP JUMPDEST PUSH32 0xF0C57E16840DF040F15088DC2F81FE391C3923BEC73E23A9662EFC9C229C6A00 DUP1 SLOAD PUSH9 0x10000000000000000 SWAP1 DIV PUSH1 0xFF AND ISZERO PUSH2 0x6E JUMPI PUSH1 0x40 MLOAD PUSH4 0xF92EE8A9 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB SWAP1 DUP2 AND EQ PUSH2 0xCD JUMPI DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB NOT AND PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB SWAP1 DUP2 OR DUP3 SSTORE PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH32 0xC7F505B2F371AE2175EE4913F4499E1F2633A7B5936321EED1CDAEB6115181D2 SWAP1 PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 JUMPDEST POP JUMP JUMPDEST PUSH2 0xC1F DUP1 PUSH2 0xDF PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0xEC JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x7CC1F867 GT PUSH2 0x8A JUMPI DUP1 PUSH4 0xC4511C6A GT PUSH2 0x59 JUMPI DUP1 PUSH4 0xC4511C6A EQ PUSH2 0x273 JUMPI DUP1 PUSH4 0xD0E30DB0 EQ PUSH2 0x293 JUMPI DUP1 PUSH4 0xE4A30116 EQ PUSH2 0x29B JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x2BB JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x7CC1F867 EQ PUSH2 0x1D2 JUMPI DUP1 PUSH4 0x8456CB59 EQ PUSH2 0x201 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x216 JUMPI DUP1 PUSH4 0x8ED83271 EQ PUSH2 0x25D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x3F4BA83A GT PUSH2 0xC6 JUMPI DUP1 PUSH4 0x3F4BA83A EQ PUSH2 0x162 JUMPI DUP1 PUSH4 0x5C975ABB EQ PUSH2 0x177 JUMPI DUP1 PUSH4 0x645006CA EQ PUSH2 0x1A7 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x1BD JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x12065FE0 EQ PUSH2 0x100 JUMPI DUP1 PUSH4 0x2A80CDA3 EQ PUSH2 0x122 JUMPI DUP1 PUSH4 0x2F940C70 EQ PUSH2 0x142 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLDATASIZE PUSH2 0xFB JUMPI PUSH2 0xF9 PUSH2 0x2DB JUMP JUMPDEST STOP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x10C JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP SELFBALANCE JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x12E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x13D CALLDATASIZE PUSH1 0x4 PUSH2 0xB25 JUMP JUMPDEST PUSH2 0x384 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x14E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x15D CALLDATASIZE PUSH1 0x4 PUSH2 0xB53 JUMP JUMPDEST PUSH2 0x488 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x16E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x594 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x183 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1B3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x10F PUSH1 0x0 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1C9 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x5A6 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1DE JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x1 SLOAD SELFBALANCE PUSH1 0x40 DUP1 MLOAD SWAP4 DUP5 MSTORE PUSH1 0x20 DUP5 ADD SWAP3 SWAP1 SWAP3 MSTORE SWAP1 DUP3 ADD MSTORE PUSH1 0x60 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x20D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x5B8 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x222 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x269 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x10F PUSH1 0x1 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x27F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x28E CALLDATASIZE PUSH1 0x4 PUSH2 0xB25 JUMP JUMPDEST PUSH2 0x5C8 JUMP JUMPDEST PUSH2 0xF9 PUSH2 0x672 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2A7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x2B6 CALLDATASIZE PUSH1 0x4 PUSH2 0xB83 JUMP JUMPDEST PUSH2 0x6B3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2C7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x2D6 CALLDATASIZE PUSH1 0x4 PUSH2 0xBA5 JUMP JUMPDEST PUSH2 0x81F JUMP JUMPDEST CALLVALUE PUSH1 0x0 DUP2 SWAP1 SUB PUSH2 0x2FE JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 SLOAD DUP2 LT ISZERO PUSH2 0x321 JUMPI PUSH1 0x40 MLOAD PUSH4 0x617AB12D PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 SLOAD DUP2 GT ISZERO PUSH2 0x344 JUMPI PUSH1 0x40 MLOAD PUSH4 0x6250401 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE TIMESTAMP PUSH1 0x20 DUP3 ADD MSTORE CALLER SWAP2 DUP3 SWAP2 PUSH32 0x90890809C654F11D6E72A28FA60149770A0D11EC6C92319D6CEB2BB0A4EA1A15 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP JUMP JUMPDEST PUSH2 0x38C PUSH2 0x85D JUMP JUMPDEST PUSH1 0x0 DUP2 GT PUSH2 0x3E1 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1D PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x416D6F756E74206D7573742062652067726561746572207468616E2030000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 SLOAD DUP2 LT PUSH2 0x442 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x27 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4D696E20616D6F756E74206D757374206265206C657373207468616E206D6178 PUSH1 0x44 DUP3 ADD MSTORE PUSH7 0x8185B5BDD5B9D PUSH1 0xCA SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD SWAP1 DUP3 SWAP1 SSTORE PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 SWAP1 MSTORE PUSH32 0x5FB4589FCDFAB8BD40D9776ABC10876BB1CB02C0EDAB28D05CC42869B40E0329 SWAP2 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP POP JUMP JUMPDEST PUSH2 0x490 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x4B7 JUMPI PUSH1 0x40 MLOAD PUSH4 0xE6C4247B PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST SELFBALANCE DUP3 GT ISZERO PUSH2 0x4D8 JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP4 PUSH1 0x40 MLOAD PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 DUP8 GAS CALL SWAP3 POP POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x525 JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x52A JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP POP SWAP1 POP DUP1 PUSH2 0x54C JUMPI PUSH1 0x40 MLOAD PUSH4 0x12171D83 PUSH1 0xE3 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0xBB43D2E6A13F52D71A3A836CF197FA240FB4EF40914B6E13CC966FD6DFB19B68 DUP5 PUSH1 0x40 MLOAD PUSH2 0x587 SWAP2 DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP POP JUMP JUMPDEST PUSH2 0x59C PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0x8B8 JUMP JUMPDEST JUMP JUMPDEST PUSH2 0x5AE PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH1 0x0 PUSH2 0x918 JUMP JUMPDEST PUSH2 0x5C0 PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0x989 JUMP JUMPDEST PUSH2 0x5D0 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x0 SLOAD DUP2 GT PUSH2 0x634 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x2A PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4D617820616D6F756E74206D7573742062652067726561746572207468616E20 PUSH1 0x44 DUP3 ADD MSTORE PUSH10 0x1B5A5B88185B5BDD5B9D PUSH1 0xB2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH1 0x1 DUP1 SLOAD SWAP1 DUP3 SWAP1 SSTORE PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 SWAP1 MSTORE PUSH32 0x2BB9E3FC7B14157E1812FDBE23016F0756B7EBB061C80589DEC1550779776C78 SWAP2 ADD PUSH2 0x47C JUMP JUMPDEST PUSH2 0x67A PUSH2 0x9D2 JUMP JUMPDEST PUSH2 0x682 PUSH2 0xA1C JUMP JUMPDEST PUSH2 0x68A PUSH2 0x2DB JUMP JUMPDEST PUSH2 0x5A4 PUSH1 0x1 PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 SSTORE JUMP JUMPDEST PUSH32 0xF0C57E16840DF040F15088DC2F81FE391C3923BEC73E23A9662EFC9C229C6A00 DUP1 SLOAD PUSH1 0x1 PUSH1 0x40 SHL DUP2 DIV PUSH1 0xFF AND ISZERO SWAP1 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH1 0x0 DUP2 ISZERO DUP1 ISZERO PUSH2 0x6F9 JUMPI POP DUP3 JUMPDEST SWAP1 POP PUSH1 0x0 DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH1 0x1 EQ DUP1 ISZERO PUSH2 0x716 JUMPI POP ADDRESS EXTCODESIZE ISZERO JUMPDEST SWAP1 POP DUP2 ISZERO DUP1 ISZERO PUSH2 0x724 JUMPI POP DUP1 ISZERO JUMPDEST ISZERO PUSH2 0x742 JUMPI PUSH1 0x40 MLOAD PUSH4 0xF92EE8A9 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP5 SLOAD PUSH8 0xFFFFFFFFFFFFFFFF NOT AND PUSH1 0x1 OR DUP6 SSTORE DUP4 ISZERO PUSH2 0x76C JUMPI DUP5 SLOAD PUSH1 0xFF PUSH1 0x40 SHL NOT AND PUSH1 0x1 PUSH1 0x40 SHL OR DUP6 SSTORE JUMPDEST DUP7 PUSH1 0x0 SUB PUSH2 0x78D JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP7 DUP7 GT PUSH2 0x7AD JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0x7B5 PUSH2 0xA73 JUMP JUMPDEST PUSH2 0x7BE CALLER PUSH2 0xA83 JUMP JUMPDEST PUSH2 0x7C6 PUSH2 0xA94 JUMP JUMPDEST PUSH1 0x0 DUP8 SWAP1 SSTORE PUSH1 0x1 DUP7 SWAP1 SSTORE DUP4 ISZERO PUSH2 0x816 JUMPI DUP5 SLOAD PUSH1 0xFF PUSH1 0x40 SHL NOT AND DUP6 SSTORE PUSH1 0x40 MLOAD PUSH1 0x1 DUP2 MSTORE PUSH32 0xC7F505B2F371AE2175EE4913F4499E1F2633A7B5936321EED1CDAEB6115181D2 SWAP1 PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 JUMPDEST POP POP POP POP POP POP POP JUMP JUMPDEST PUSH2 0x827 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x851 JUMPI PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH2 0x85A DUP2 PUSH2 0x918 JUMP JUMPDEST POP JUMP JUMPDEST CALLER PUSH2 0x88F PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND EQ PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH2 0x8C0 PUSH2 0xA9C JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE DUP1 SLOAD PUSH1 0xFF NOT AND DUP2 SSTORE PUSH32 0x5DB9EE0A495BF2E6FF9C91A7834C1BA4FDD244A5E8AA4E537BD38AEAE4B073AA CALLER JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP5 DUP2 AND SWAP2 DUP3 OR DUP5 SSTORE PUSH1 0x40 MLOAD SWAP3 AND SWAP2 DUP3 SWAP1 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP1 PUSH1 0x0 SWAP1 LOG3 POP POP POP JUMP JUMPDEST PUSH2 0x991 PUSH2 0xA1C JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR DUP2 SSTORE PUSH32 0x62E78CEA01BEE320CD4E420270B5EA74000D11B0C9F74754EBDBFC544B05A258 CALLER PUSH2 0x8FA JUMP JUMPDEST PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 DUP1 SLOAD PUSH1 0x1 NOT ADD PUSH2 0xA16 JUMPI PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x2 SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND ISZERO PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0xD93C0665 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 SSTORE JUMP JUMPDEST PUSH2 0xA7B PUSH2 0xACC JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0xB15 JUMP JUMPDEST PUSH2 0xA8B PUSH2 0xACC JUMP JUMPDEST PUSH2 0x85A DUP2 PUSH2 0xB1D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0xACC JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x8DFC202B PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH32 0xF0C57E16840DF040F15088DC2F81FE391C3923BEC73E23A9662EFC9C229C6A00 SLOAD PUSH1 0x1 PUSH1 0x40 SHL SWAP1 DIV PUSH1 0xFF AND PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x1AFCD79F PUSH1 0xE3 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0xA4D PUSH2 0xACC JUMP JUMPDEST PUSH2 0x827 PUSH2 0xACC JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xB37 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0x85A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xB66 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD SWAP2 POP PUSH1 0x20 DUP4 ADD CALLDATALOAD PUSH2 0xB78 DUP2 PUSH2 0xB3E JUMP JUMPDEST DUP1 SWAP2 POP POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xB96 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP POP DUP1 CALLDATALOAD SWAP3 PUSH1 0x20 SWAP1 SWAP2 ADD CALLDATALOAD SWAP2 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xBB7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH2 0xBC2 DUP2 PUSH2 0xB3E JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP INVALID 0xCD 0x5E 0xD1 0x5C PUSH15 0x187E77E9AEE88184C21F4F2182AB58 0x27 0xCB EXTCODESIZE PUSH31 0x7FBEDCD63F03300A26469706673582212207AF51485474667EA91105C564C CALLER 0xBA LOG0 LOG3 0x28 0xBA OR 0xE3 0xED SWAP11 0x1F 0xCE SWAP14 0xF9 0x2B 0xEB CALLVALUE 0x4C 0xD6 PUSH5 0x736F6C6343 STOP ADDMOD AND STOP CALLER ", "sourceMap": "580:4795:5:-:0;;;1397:53;;;;;;;;;-1:-1:-1;1421:22:5;:20;:22::i;:::-;580:4795;;7709:422:1;3147:66;7898:15;;;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:1;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:1;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:1;-1:-1:-1;;;;;8033:33:1;;;;;8085:29;;158:50:6;;;8085:29:1;;146:2:6;131:18;8085:29:1;;;;;;;7979:146;7758:373;7709:422::o;14:200:6:-;580:4795:5;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@_924": {"entryPoint": null, "id": 924, "parameterSlots": 0, "returnSlots": 0}, "@__Ownable_init_54": {"entryPoint": 2691, "id": 54, "parameterSlots": 1, "returnSlots": 0}, "@__Ownable_init_unchained_81": {"entryPoint": 2845, "id": 81, "parameterSlots": 1, "returnSlots": 0}, "@__Pausable_init_572": {"entryPoint": 2708, "id": 572, "parameterSlots": 0, "returnSlots": 0}, "@__ReentrancyGuard_init_708": {"entryPoint": 2675, "id": 708, "parameterSlots": 0, "returnSlots": 0}, "@__ReentrancyGuard_init_unchained_726": {"entryPoint": 2837, "id": 726, "parameterSlots": 0, "returnSlots": 0}, "@_checkInitializing_370": {"entryPoint": 2764, "id": 370, "parameterSlots": 0, "returnSlots": 0}, "@_checkOwner_122": {"entryPoint": 2141, "id": 122, "parameterSlots": 0, "returnSlots": 0}, "@_getInitializableStorage_461": {"entryPoint": null, "id": 461, "parameterSlots": 0, "returnSlots": 1}, "@_getOwnableStorage_25": {"entryPoint": null, "id": 25, "parameterSlots": 0, "returnSlots": 1}, "@_getPausableStorage_534": {"entryPoint": null, "id": 534, "parameterSlots": 0, "returnSlots": 1}, "@_getReentrancyGuardStorage_696": {"entryPoint": null, "id": 696, "parameterSlots": 0, "returnSlots": 1}, "@_initializableStorageSlot_447": {"entryPoint": null, "id": 447, "parameterSlots": 0, "returnSlots": 1}, "@_isInitializing_438": {"entryPoint": null, "id": 438, "parameterSlots": 0, "returnSlots": 1}, "@_msgSender_490": {"entryPoint": null, "id": 490, "parameterSlots": 0, "returnSlots": 1}, "@_nonReentrantAfter_778": {"entryPoint": 2637, "id": 778, "parameterSlots": 0, "returnSlots": 0}, "@_nonReentrantBefore_762": {"entryPoint": 2514, "id": 762, "parameterSlots": 0, "returnSlots": 0}, "@_pause_643": {"entryPoint": 2441, "id": 643, "parameterSlots": 0, "returnSlots": 0}, "@_processDeposit_967": {"entryPoint": 731, "id": 967, "parameterSlots": 0, "returnSlots": 0}, "@_requireNotPaused_606": {"entryPoint": 2588, "id": 606, "parameterSlots": 0, "returnSlots": 0}, "@_requirePaused_619": {"entryPoint": 2716, "id": 619, "parameterSlots": 0, "returnSlots": 0}, "@_transferOwnership_193": {"entryPoint": 2328, "id": 193, "parameterSlots": 1, "returnSlots": 0}, "@_unpause_667": {"entryPoint": 2232, "id": 667, "parameterSlots": 0, "returnSlots": 0}, "@deposit_916": {"entryPoint": 1650, "id": 916, "parameterSlots": 0, "returnSlots": 0}, "@emergencyWithdraw_1104": {"entryPoint": 1160, "id": 1104, "parameterSlots": 2, "returnSlots": 0}, "@getBalance_1117": {"entryPoint": null, "id": 1117, "parameterSlots": 0, "returnSlots": 1}, "@getContractInfo_1137": {"entryPoint": null, "id": 1137, "parameterSlots": 0, "returnSlots": 3}, "@initialize_904": {"entryPoint": 1715, "id": 904, "parameterSlots": 2, "returnSlots": 0}, "@maxDepositAmount_816": {"entryPoint": null, "id": 816, "parameterSlots": 0, "returnSlots": 0}, "@minDepositAmount_814": {"entryPoint": null, "id": 814, "parameterSlots": 0, "returnSlots": 0}, "@owner_105": {"entryPoint": null, "id": 105, "parameterSlots": 0, "returnSlots": 1}, "@pause_1042": {"entryPoint": 1464, "id": 1042, "parameterSlots": 0, "returnSlots": 0}, "@paused_594": {"entryPoint": null, "id": 594, "parameterSlots": 0, "returnSlots": 1}, "@renounceOwnership_136": {"entryPoint": 1446, "id": 136, "parameterSlots": 0, "returnSlots": 0}, "@setMaxDepositAmount_1032": {"entryPoint": 1480, "id": 1032, "parameterSlots": 1, "returnSlots": 0}, "@setMinDepositAmount_1003": {"entryPoint": 900, "id": 1003, "parameterSlots": 1, "returnSlots": 0}, "@transferOwnership_164": {"entryPoint": 2079, "id": 164, "parameterSlots": 1, "returnSlots": 0}, "@unpause_1052": {"entryPoint": 1428, "id": 1052, "parameterSlots": 0, "returnSlots": 0}, "abi_decode_tuple_t_address": {"entryPoint": 2981, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint256": {"entryPoint": 2853, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint256t_address_payable": {"entryPoint": 2899, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_decode_tuple_t_uint256t_uint256": {"entryPoint": 2947, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_encode_tuple_packed_t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_address__to_t_address__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_rational_1_by_1__to_t_uint64__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_3e76f273c719bb7d23db533a2dc9a822ae7d899fcd42eb8910272e24764e8296__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_446e99ffdf2e03d3177d40dd993d55d45602fea049771b76ceb15b1e4a0f2a97__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_caf27fc83f7a2f76ea0e23c346f21e48964232371e524260eef1e330d3edebe4__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_encode_tuple_t_uint256_t_uint256_t_uint256__to_t_uint256_t_uint256_t_uint256__fromStack_reversed": {"entryPoint": null, "id": null, "parameterSlots": 4, "returnSlots": 1}, "validator_revert_address_payable": {"entryPoint": 2878, "id": null, "parameterSlots": 1, "returnSlots": 0}}, "generatedSources": [{"ast": {"nativeSrc": "0:3949:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:3949:6", "statements": [{"nativeSrc": "6:3:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:6", "statements": []}, {"body": {"nativeSrc": "115:76:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "115:76:6", "statements": [{"nativeSrc": "125:26:6", "nodeType": "YulAssignment", "src": "125:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "137:9:6", "nodeType": "YulIdentifier", "src": "137:9:6"}, {"kind": "number", "nativeSrc": "148:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "148:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "133:3:6", "nodeType": "YulIdentifier", "src": "133:3:6"}, "nativeSrc": "133:18:6", "nodeType": "YulFunctionCall", "src": "133:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "125:4:6", "nodeType": "YulIdentifier", "src": "125:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "167:9:6", "nodeType": "YulIdentifier", "src": "167:9:6"}, {"name": "value0", "nativeSrc": "178:6:6", "nodeType": "YulIdentifier", "src": "178:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "160:6:6", "nodeType": "YulIdentifier", "src": "160:6:6"}, "nativeSrc": "160:25:6", "nodeType": "YulFunctionCall", "src": "160:25:6"}, "nativeSrc": "160:25:6", "nodeType": "YulExpressionStatement", "src": "160:25:6"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nativeSrc": "14:177:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "84:9:6", "nodeType": "YulTypedName", "src": "84:9:6", "type": ""}, {"name": "value0", "nativeSrc": "95:6:6", "nodeType": "YulTypedName", "src": "95:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "106:4:6", "nodeType": "YulTypedName", "src": "106:4:6", "type": ""}], "src": "14:177:6"}, {"body": {"nativeSrc": "266:110:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "266:110:6", "statements": [{"body": {"nativeSrc": "312:16:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "312:16:6", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "321:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "321:1:6", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "324:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "324:1:6", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "314:6:6", "nodeType": "YulIdentifier", "src": "314:6:6"}, "nativeSrc": "314:12:6", "nodeType": "YulFunctionCall", "src": "314:12:6"}, "nativeSrc": "314:12:6", "nodeType": "YulExpressionStatement", "src": "314:12:6"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "287:7:6", "nodeType": "YulIdentifier", "src": "287:7:6"}, {"name": "headStart", "nativeSrc": "296:9:6", "nodeType": "YulIdentifier", "src": "296:9:6"}], "functionName": {"name": "sub", "nativeSrc": "283:3:6", "nodeType": "YulIdentifier", "src": "283:3:6"}, "nativeSrc": "283:23:6", "nodeType": "YulFunctionCall", "src": "283:23:6"}, {"kind": "number", "nativeSrc": "308:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "308:2:6", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "279:3:6", "nodeType": "YulIdentifier", "src": "279:3:6"}, "nativeSrc": "279:32:6", "nodeType": "YulFunctionCall", "src": "279:32:6"}, "nativeSrc": "276:52:6", "nodeType": "YulIf", "src": "276:52:6"}, {"nativeSrc": "337:33:6", "nodeType": "YulAssignment", "src": "337:33:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "360:9:6", "nodeType": "YulIdentifier", "src": "360:9:6"}], "functionName": {"name": "calldataload", "nativeSrc": "347:12:6", "nodeType": "YulIdentifier", "src": "347:12:6"}, "nativeSrc": "347:23:6", "nodeType": "YulFunctionCall", "src": "347:23:6"}, "variableNames": [{"name": "value0", "nativeSrc": "337:6:6", "nodeType": "YulIdentifier", "src": "337:6:6"}]}]}, "name": "abi_decode_tuple_t_uint256", "nativeSrc": "196:180:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "232:9:6", "nodeType": "YulTypedName", "src": "232:9:6", "type": ""}, {"name": "dataEnd", "nativeSrc": "243:7:6", "nodeType": "YulTypedName", "src": "243:7:6", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "255:6:6", "nodeType": "YulTypedName", "src": "255:6:6", "type": ""}], "src": "196:180:6"}, {"body": {"nativeSrc": "434:86:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "434:86:6", "statements": [{"body": {"nativeSrc": "498:16:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "498:16:6", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "507:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "507:1:6", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "510:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "510:1:6", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "500:6:6", "nodeType": "YulIdentifier", "src": "500:6:6"}, "nativeSrc": "500:12:6", "nodeType": "YulFunctionCall", "src": "500:12:6"}, "nativeSrc": "500:12:6", "nodeType": "YulExpressionStatement", "src": "500:12:6"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "457:5:6", "nodeType": "YulIdentifier", "src": "457:5:6"}, {"arguments": [{"name": "value", "nativeSrc": "468:5:6", "nodeType": "YulIdentifier", "src": "468:5:6"}, {"arguments": [{"arguments": [{"kind": "number", "nativeSrc": "483:3:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "483:3:6", "type": "", "value": "160"}, {"kind": "number", "nativeSrc": "488:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "488:1:6", "type": "", "value": "1"}], "functionName": {"name": "shl", "nativeSrc": "479:3:6", "nodeType": "YulIdentifier", "src": "479:3:6"}, "nativeSrc": "479:11:6", "nodeType": "YulFunctionCall", "src": "479:11:6"}, {"kind": "number", "nativeSrc": "492:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "492:1:6", "type": "", "value": "1"}], "functionName": {"name": "sub", "nativeSrc": "475:3:6", "nodeType": "YulIdentifier", "src": "475:3:6"}, "nativeSrc": "475:19:6", "nodeType": "YulFunctionCall", "src": "475:19:6"}], "functionName": {"name": "and", "nativeSrc": "464:3:6", "nodeType": "YulIdentifier", "src": "464:3:6"}, "nativeSrc": "464:31:6", "nodeType": "YulFunctionCall", "src": "464:31:6"}], "functionName": {"name": "eq", "nativeSrc": "454:2:6", "nodeType": "YulIdentifier", "src": "454:2:6"}, "nativeSrc": "454:42:6", "nodeType": "YulFunctionCall", "src": "454:42:6"}], "functionName": {"name": "iszero", "nativeSrc": "447:6:6", "nodeType": "YulIdentifier", "src": "447:6:6"}, "nativeSrc": "447:50:6", "nodeType": "YulFunctionCall", "src": "447:50:6"}, "nativeSrc": "444:70:6", "nodeType": "YulIf", "src": "444:70:6"}]}, "name": "validator_revert_address_payable", "nativeSrc": "381:139:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "423:5:6", "nodeType": "YulTypedName", "src": "423:5:6", "type": ""}], "src": "381:139:6"}, {"body": {"nativeSrc": "620:236:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "620:236:6", "statements": [{"body": {"nativeSrc": "666:16:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "666:16:6", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "675:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "675:1:6", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "678:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "678:1:6", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "668:6:6", "nodeType": "YulIdentifier", "src": "668:6:6"}, "nativeSrc": "668:12:6", "nodeType": "YulFunctionCall", "src": "668:12:6"}, "nativeSrc": "668:12:6", "nodeType": "YulExpressionStatement", "src": "668:12:6"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "641:7:6", "nodeType": "YulIdentifier", "src": "641:7:6"}, {"name": "headStart", "nativeSrc": "650:9:6", "nodeType": "YulIdentifier", "src": "650:9:6"}], "functionName": {"name": "sub", "nativeSrc": "637:3:6", "nodeType": "YulIdentifier", "src": "637:3:6"}, "nativeSrc": "637:23:6", "nodeType": "YulFunctionCall", "src": "637:23:6"}, {"kind": "number", "nativeSrc": "662:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "662:2:6", "type": "", "value": "64"}], "functionName": {"name": "slt", "nativeSrc": "633:3:6", "nodeType": "YulIdentifier", "src": "633:3:6"}, "nativeSrc": "633:32:6", "nodeType": "YulFunctionCall", "src": "633:32:6"}, "nativeSrc": "630:52:6", "nodeType": "YulIf", "src": "630:52:6"}, {"nativeSrc": "691:33:6", "nodeType": "YulAssignment", "src": "691:33:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "714:9:6", "nodeType": "YulIdentifier", "src": "714:9:6"}], "functionName": {"name": "calldataload", "nativeSrc": "701:12:6", "nodeType": "YulIdentifier", "src": "701:12:6"}, "nativeSrc": "701:23:6", "nodeType": "YulFunctionCall", "src": "701:23:6"}, "variableNames": [{"name": "value0", "nativeSrc": "691:6:6", "nodeType": "YulIdentifier", "src": "691:6:6"}]}, {"nativeSrc": "733:45:6", "nodeType": "YulVariableDeclaration", "src": "733:45:6", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "763:9:6", "nodeType": "YulIdentifier", "src": "763:9:6"}, {"kind": "number", "nativeSrc": "774:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "774:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "759:3:6", "nodeType": "YulIdentifier", "src": "759:3:6"}, "nativeSrc": "759:18:6", "nodeType": "YulFunctionCall", "src": "759:18:6"}], "functionName": {"name": "calldataload", "nativeSrc": "746:12:6", "nodeType": "YulIdentifier", "src": "746:12:6"}, "nativeSrc": "746:32:6", "nodeType": "YulFunctionCall", "src": "746:32:6"}, "variables": [{"name": "value", "nativeSrc": "737:5:6", "nodeType": "YulTypedName", "src": "737:5:6", "type": ""}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "820:5:6", "nodeType": "YulIdentifier", "src": "820:5:6"}], "functionName": {"name": "validator_revert_address_payable", "nativeSrc": "787:32:6", "nodeType": "YulIdentifier", "src": "787:32:6"}, "nativeSrc": "787:39:6", "nodeType": "YulFunctionCall", "src": "787:39:6"}, "nativeSrc": "787:39:6", "nodeType": "YulExpressionStatement", "src": "787:39:6"}, {"nativeSrc": "835:15:6", "nodeType": "YulAssignment", "src": "835:15:6", "value": {"name": "value", "nativeSrc": "845:5:6", "nodeType": "YulIdentifier", "src": "845:5:6"}, "variableNames": [{"name": "value1", "nativeSrc": "835:6:6", "nodeType": "YulIdentifier", "src": "835:6:6"}]}]}, "name": "abi_decode_tuple_t_uint256t_address_payable", "nativeSrc": "525:331:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "578:9:6", "nodeType": "YulTypedName", "src": "578:9:6", "type": ""}, {"name": "dataEnd", "nativeSrc": "589:7:6", "nodeType": "YulTypedName", "src": "589:7:6", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "601:6:6", "nodeType": "YulTypedName", "src": "601:6:6", "type": ""}, {"name": "value1", "nativeSrc": "609:6:6", "nodeType": "YulTypedName", "src": "609:6:6", "type": ""}], "src": "525:331:6"}, {"body": {"nativeSrc": "956:92:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "956:92:6", "statements": [{"nativeSrc": "966:26:6", "nodeType": "YulAssignment", "src": "966:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "978:9:6", "nodeType": "YulIdentifier", "src": "978:9:6"}, {"kind": "number", "nativeSrc": "989:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "989:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "974:3:6", "nodeType": "YulIdentifier", "src": "974:3:6"}, "nativeSrc": "974:18:6", "nodeType": "YulFunctionCall", "src": "974:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "966:4:6", "nodeType": "YulIdentifier", "src": "966:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "1008:9:6", "nodeType": "YulIdentifier", "src": "1008:9:6"}, {"arguments": [{"arguments": [{"name": "value0", "nativeSrc": "1033:6:6", "nodeType": "YulIdentifier", "src": "1033:6:6"}], "functionName": {"name": "iszero", "nativeSrc": "1026:6:6", "nodeType": "YulIdentifier", "src": "1026:6:6"}, "nativeSrc": "1026:14:6", "nodeType": "YulFunctionCall", "src": "1026:14:6"}], "functionName": {"name": "iszero", "nativeSrc": "1019:6:6", "nodeType": "YulIdentifier", "src": "1019:6:6"}, "nativeSrc": "1019:22:6", "nodeType": "YulFunctionCall", "src": "1019:22:6"}], "functionName": {"name": "mstore", "nativeSrc": "1001:6:6", "nodeType": "YulIdentifier", "src": "1001:6:6"}, "nativeSrc": "1001:41:6", "nodeType": "YulFunctionCall", "src": "1001:41:6"}, "nativeSrc": "1001:41:6", "nodeType": "YulExpressionStatement", "src": "1001:41:6"}]}, "name": "abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed", "nativeSrc": "861:187:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "925:9:6", "nodeType": "YulTypedName", "src": "925:9:6", "type": ""}, {"name": "value0", "nativeSrc": "936:6:6", "nodeType": "YulTypedName", "src": "936:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "947:4:6", "nodeType": "YulTypedName", "src": "947:4:6", "type": ""}], "src": "861:187:6"}, {"body": {"nativeSrc": "1210:162:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1210:162:6", "statements": [{"nativeSrc": "1220:26:6", "nodeType": "YulAssignment", "src": "1220:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "1232:9:6", "nodeType": "YulIdentifier", "src": "1232:9:6"}, {"kind": "number", "nativeSrc": "1243:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1243:2:6", "type": "", "value": "96"}], "functionName": {"name": "add", "nativeSrc": "1228:3:6", "nodeType": "YulIdentifier", "src": "1228:3:6"}, "nativeSrc": "1228:18:6", "nodeType": "YulFunctionCall", "src": "1228:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "1220:4:6", "nodeType": "YulIdentifier", "src": "1220:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "1262:9:6", "nodeType": "YulIdentifier", "src": "1262:9:6"}, {"name": "value0", "nativeSrc": "1273:6:6", "nodeType": "YulIdentifier", "src": "1273:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "1255:6:6", "nodeType": "YulIdentifier", "src": "1255:6:6"}, "nativeSrc": "1255:25:6", "nodeType": "YulFunctionCall", "src": "1255:25:6"}, "nativeSrc": "1255:25:6", "nodeType": "YulExpressionStatement", "src": "1255:25:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "1300:9:6", "nodeType": "YulIdentifier", "src": "1300:9:6"}, {"kind": "number", "nativeSrc": "1311:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1311:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "1296:3:6", "nodeType": "YulIdentifier", "src": "1296:3:6"}, "nativeSrc": "1296:18:6", "nodeType": "YulFunctionCall", "src": "1296:18:6"}, {"name": "value1", "nativeSrc": "1316:6:6", "nodeType": "YulIdentifier", "src": "1316:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "1289:6:6", "nodeType": "YulIdentifier", "src": "1289:6:6"}, "nativeSrc": "1289:34:6", "nodeType": "YulFunctionCall", "src": "1289:34:6"}, "nativeSrc": "1289:34:6", "nodeType": "YulExpressionStatement", "src": "1289:34:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "1343:9:6", "nodeType": "YulIdentifier", "src": "1343:9:6"}, {"kind": "number", "nativeSrc": "1354:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1354:2:6", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "1339:3:6", "nodeType": "YulIdentifier", "src": "1339:3:6"}, "nativeSrc": "1339:18:6", "nodeType": "YulFunctionCall", "src": "1339:18:6"}, {"name": "value2", "nativeSrc": "1359:6:6", "nodeType": "YulIdentifier", "src": "1359:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "1332:6:6", "nodeType": "YulIdentifier", "src": "1332:6:6"}, "nativeSrc": "1332:34:6", "nodeType": "YulFunctionCall", "src": "1332:34:6"}, "nativeSrc": "1332:34:6", "nodeType": "YulExpressionStatement", "src": "1332:34:6"}]}, "name": "abi_encode_tuple_t_uint256_t_uint256_t_uint256__to_t_uint256_t_uint256_t_uint256__fromStack_reversed", "nativeSrc": "1053:319:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1163:9:6", "nodeType": "YulTypedName", "src": "1163:9:6", "type": ""}, {"name": "value2", "nativeSrc": "1174:6:6", "nodeType": "YulTypedName", "src": "1174:6:6", "type": ""}, {"name": "value1", "nativeSrc": "1182:6:6", "nodeType": "YulTypedName", "src": "1182:6:6", "type": ""}, {"name": "value0", "nativeSrc": "1190:6:6", "nodeType": "YulTypedName", "src": "1190:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "1201:4:6", "nodeType": "YulTypedName", "src": "1201:4:6", "type": ""}], "src": "1053:319:6"}, {"body": {"nativeSrc": "1478:102:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1478:102:6", "statements": [{"nativeSrc": "1488:26:6", "nodeType": "YulAssignment", "src": "1488:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "1500:9:6", "nodeType": "YulIdentifier", "src": "1500:9:6"}, {"kind": "number", "nativeSrc": "1511:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1511:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "1496:3:6", "nodeType": "YulIdentifier", "src": "1496:3:6"}, "nativeSrc": "1496:18:6", "nodeType": "YulFunctionCall", "src": "1496:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "1488:4:6", "nodeType": "YulIdentifier", "src": "1488:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "1530:9:6", "nodeType": "YulIdentifier", "src": "1530:9:6"}, {"arguments": [{"name": "value0", "nativeSrc": "1545:6:6", "nodeType": "YulIdentifier", "src": "1545:6:6"}, {"arguments": [{"arguments": [{"kind": "number", "nativeSrc": "1561:3:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1561:3:6", "type": "", "value": "160"}, {"kind": "number", "nativeSrc": "1566:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1566:1:6", "type": "", "value": "1"}], "functionName": {"name": "shl", "nativeSrc": "1557:3:6", "nodeType": "YulIdentifier", "src": "1557:3:6"}, "nativeSrc": "1557:11:6", "nodeType": "YulFunctionCall", "src": "1557:11:6"}, {"kind": "number", "nativeSrc": "1570:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1570:1:6", "type": "", "value": "1"}], "functionName": {"name": "sub", "nativeSrc": "1553:3:6", "nodeType": "YulIdentifier", "src": "1553:3:6"}, "nativeSrc": "1553:19:6", "nodeType": "YulFunctionCall", "src": "1553:19:6"}], "functionName": {"name": "and", "nativeSrc": "1541:3:6", "nodeType": "YulIdentifier", "src": "1541:3:6"}, "nativeSrc": "1541:32:6", "nodeType": "YulFunctionCall", "src": "1541:32:6"}], "functionName": {"name": "mstore", "nativeSrc": "1523:6:6", "nodeType": "YulIdentifier", "src": "1523:6:6"}, "nativeSrc": "1523:51:6", "nodeType": "YulFunctionCall", "src": "1523:51:6"}, "nativeSrc": "1523:51:6", "nodeType": "YulExpressionStatement", "src": "1523:51:6"}]}, "name": "abi_encode_tuple_t_address__to_t_address__fromStack_reversed", "nativeSrc": "1377:203:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1447:9:6", "nodeType": "YulTypedName", "src": "1447:9:6", "type": ""}, {"name": "value0", "nativeSrc": "1458:6:6", "nodeType": "YulTypedName", "src": "1458:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "1469:4:6", "nodeType": "YulTypedName", "src": "1469:4:6", "type": ""}], "src": "1377:203:6"}, {"body": {"nativeSrc": "1672:161:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1672:161:6", "statements": [{"body": {"nativeSrc": "1718:16:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1718:16:6", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "1727:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1727:1:6", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "1730:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1730:1:6", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "1720:6:6", "nodeType": "YulIdentifier", "src": "1720:6:6"}, "nativeSrc": "1720:12:6", "nodeType": "YulFunctionCall", "src": "1720:12:6"}, "nativeSrc": "1720:12:6", "nodeType": "YulExpressionStatement", "src": "1720:12:6"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "1693:7:6", "nodeType": "YulIdentifier", "src": "1693:7:6"}, {"name": "headStart", "nativeSrc": "1702:9:6", "nodeType": "YulIdentifier", "src": "1702:9:6"}], "functionName": {"name": "sub", "nativeSrc": "1689:3:6", "nodeType": "YulIdentifier", "src": "1689:3:6"}, "nativeSrc": "1689:23:6", "nodeType": "YulFunctionCall", "src": "1689:23:6"}, {"kind": "number", "nativeSrc": "1714:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1714:2:6", "type": "", "value": "64"}], "functionName": {"name": "slt", "nativeSrc": "1685:3:6", "nodeType": "YulIdentifier", "src": "1685:3:6"}, "nativeSrc": "1685:32:6", "nodeType": "YulFunctionCall", "src": "1685:32:6"}, "nativeSrc": "1682:52:6", "nodeType": "YulIf", "src": "1682:52:6"}, {"nativeSrc": "1743:33:6", "nodeType": "YulAssignment", "src": "1743:33:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "1766:9:6", "nodeType": "YulIdentifier", "src": "1766:9:6"}], "functionName": {"name": "calldataload", "nativeSrc": "1753:12:6", "nodeType": "YulIdentifier", "src": "1753:12:6"}, "nativeSrc": "1753:23:6", "nodeType": "YulFunctionCall", "src": "1753:23:6"}, "variableNames": [{"name": "value0", "nativeSrc": "1743:6:6", "nodeType": "YulIdentifier", "src": "1743:6:6"}]}, {"nativeSrc": "1785:42:6", "nodeType": "YulAssignment", "src": "1785:42:6", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "1812:9:6", "nodeType": "YulIdentifier", "src": "1812:9:6"}, {"kind": "number", "nativeSrc": "1823:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1823:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "1808:3:6", "nodeType": "YulIdentifier", "src": "1808:3:6"}, "nativeSrc": "1808:18:6", "nodeType": "YulFunctionCall", "src": "1808:18:6"}], "functionName": {"name": "calldataload", "nativeSrc": "1795:12:6", "nodeType": "YulIdentifier", "src": "1795:12:6"}, "nativeSrc": "1795:32:6", "nodeType": "YulFunctionCall", "src": "1795:32:6"}, "variableNames": [{"name": "value1", "nativeSrc": "1785:6:6", "nodeType": "YulIdentifier", "src": "1785:6:6"}]}]}, "name": "abi_decode_tuple_t_uint256t_uint256", "nativeSrc": "1585:248:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1630:9:6", "nodeType": "YulTypedName", "src": "1630:9:6", "type": ""}, {"name": "dataEnd", "nativeSrc": "1641:7:6", "nodeType": "YulTypedName", "src": "1641:7:6", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "1653:6:6", "nodeType": "YulTypedName", "src": "1653:6:6", "type": ""}, {"name": "value1", "nativeSrc": "1661:6:6", "nodeType": "YulTypedName", "src": "1661:6:6", "type": ""}], "src": "1585:248:6"}, {"body": {"nativeSrc": "1908:185:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1908:185:6", "statements": [{"body": {"nativeSrc": "1954:16:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1954:16:6", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "1963:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1963:1:6", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "1966:1:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1966:1:6", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "1956:6:6", "nodeType": "YulIdentifier", "src": "1956:6:6"}, "nativeSrc": "1956:12:6", "nodeType": "YulFunctionCall", "src": "1956:12:6"}, "nativeSrc": "1956:12:6", "nodeType": "YulExpressionStatement", "src": "1956:12:6"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "1929:7:6", "nodeType": "YulIdentifier", "src": "1929:7:6"}, {"name": "headStart", "nativeSrc": "1938:9:6", "nodeType": "YulIdentifier", "src": "1938:9:6"}], "functionName": {"name": "sub", "nativeSrc": "1925:3:6", "nodeType": "YulIdentifier", "src": "1925:3:6"}, "nativeSrc": "1925:23:6", "nodeType": "YulFunctionCall", "src": "1925:23:6"}, {"kind": "number", "nativeSrc": "1950:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1950:2:6", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "1921:3:6", "nodeType": "YulIdentifier", "src": "1921:3:6"}, "nativeSrc": "1921:32:6", "nodeType": "YulFunctionCall", "src": "1921:32:6"}, "nativeSrc": "1918:52:6", "nodeType": "YulIf", "src": "1918:52:6"}, {"nativeSrc": "1979:36:6", "nodeType": "YulVariableDeclaration", "src": "1979:36:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "2005:9:6", "nodeType": "YulIdentifier", "src": "2005:9:6"}], "functionName": {"name": "calldataload", "nativeSrc": "1992:12:6", "nodeType": "YulIdentifier", "src": "1992:12:6"}, "nativeSrc": "1992:23:6", "nodeType": "YulFunctionCall", "src": "1992:23:6"}, "variables": [{"name": "value", "nativeSrc": "1983:5:6", "nodeType": "YulTypedName", "src": "1983:5:6", "type": ""}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "2057:5:6", "nodeType": "YulIdentifier", "src": "2057:5:6"}], "functionName": {"name": "validator_revert_address_payable", "nativeSrc": "2024:32:6", "nodeType": "YulIdentifier", "src": "2024:32:6"}, "nativeSrc": "2024:39:6", "nodeType": "YulFunctionCall", "src": "2024:39:6"}, "nativeSrc": "2024:39:6", "nodeType": "YulExpressionStatement", "src": "2024:39:6"}, {"nativeSrc": "2072:15:6", "nodeType": "YulAssignment", "src": "2072:15:6", "value": {"name": "value", "nativeSrc": "2082:5:6", "nodeType": "YulIdentifier", "src": "2082:5:6"}, "variableNames": [{"name": "value0", "nativeSrc": "2072:6:6", "nodeType": "YulIdentifier", "src": "2072:6:6"}]}]}, "name": "abi_decode_tuple_t_address", "nativeSrc": "1838:255:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1874:9:6", "nodeType": "YulTypedName", "src": "1874:9:6", "type": ""}, {"name": "dataEnd", "nativeSrc": "1885:7:6", "nodeType": "YulTypedName", "src": "1885:7:6", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "1897:6:6", "nodeType": "YulTypedName", "src": "1897:6:6", "type": ""}], "src": "1838:255:6"}, {"body": {"nativeSrc": "2227:119:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2227:119:6", "statements": [{"nativeSrc": "2237:26:6", "nodeType": "YulAssignment", "src": "2237:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "2249:9:6", "nodeType": "YulIdentifier", "src": "2249:9:6"}, {"kind": "number", "nativeSrc": "2260:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2260:2:6", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "2245:3:6", "nodeType": "YulIdentifier", "src": "2245:3:6"}, "nativeSrc": "2245:18:6", "nodeType": "YulFunctionCall", "src": "2245:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "2237:4:6", "nodeType": "YulIdentifier", "src": "2237:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "2279:9:6", "nodeType": "YulIdentifier", "src": "2279:9:6"}, {"name": "value0", "nativeSrc": "2290:6:6", "nodeType": "YulIdentifier", "src": "2290:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "2272:6:6", "nodeType": "YulIdentifier", "src": "2272:6:6"}, "nativeSrc": "2272:25:6", "nodeType": "YulFunctionCall", "src": "2272:25:6"}, "nativeSrc": "2272:25:6", "nodeType": "YulExpressionStatement", "src": "2272:25:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2317:9:6", "nodeType": "YulIdentifier", "src": "2317:9:6"}, {"kind": "number", "nativeSrc": "2328:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2328:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2313:3:6", "nodeType": "YulIdentifier", "src": "2313:3:6"}, "nativeSrc": "2313:18:6", "nodeType": "YulFunctionCall", "src": "2313:18:6"}, {"name": "value1", "nativeSrc": "2333:6:6", "nodeType": "YulIdentifier", "src": "2333:6:6"}], "functionName": {"name": "mstore", "nativeSrc": "2306:6:6", "nodeType": "YulIdentifier", "src": "2306:6:6"}, "nativeSrc": "2306:34:6", "nodeType": "YulFunctionCall", "src": "2306:34:6"}, "nativeSrc": "2306:34:6", "nodeType": "YulExpressionStatement", "src": "2306:34:6"}]}, "name": "abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed", "nativeSrc": "2098:248:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "2188:9:6", "nodeType": "YulTypedName", "src": "2188:9:6", "type": ""}, {"name": "value1", "nativeSrc": "2199:6:6", "nodeType": "YulTypedName", "src": "2199:6:6", "type": ""}, {"name": "value0", "nativeSrc": "2207:6:6", "nodeType": "YulTypedName", "src": "2207:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "2218:4:6", "nodeType": "YulTypedName", "src": "2218:4:6", "type": ""}], "src": "2098:248:6"}, {"body": {"nativeSrc": "2525:179:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2525:179:6", "statements": [{"expression": {"arguments": [{"name": "headStart", "nativeSrc": "2542:9:6", "nodeType": "YulIdentifier", "src": "2542:9:6"}, {"kind": "number", "nativeSrc": "2553:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2553:2:6", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nativeSrc": "2535:6:6", "nodeType": "YulIdentifier", "src": "2535:6:6"}, "nativeSrc": "2535:21:6", "nodeType": "YulFunctionCall", "src": "2535:21:6"}, "nativeSrc": "2535:21:6", "nodeType": "YulExpressionStatement", "src": "2535:21:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2576:9:6", "nodeType": "YulIdentifier", "src": "2576:9:6"}, {"kind": "number", "nativeSrc": "2587:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2587:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2572:3:6", "nodeType": "YulIdentifier", "src": "2572:3:6"}, "nativeSrc": "2572:18:6", "nodeType": "YulFunctionCall", "src": "2572:18:6"}, {"kind": "number", "nativeSrc": "2592:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2592:2:6", "type": "", "value": "29"}], "functionName": {"name": "mstore", "nativeSrc": "2565:6:6", "nodeType": "YulIdentifier", "src": "2565:6:6"}, "nativeSrc": "2565:30:6", "nodeType": "YulFunctionCall", "src": "2565:30:6"}, "nativeSrc": "2565:30:6", "nodeType": "YulExpressionStatement", "src": "2565:30:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2615:9:6", "nodeType": "YulIdentifier", "src": "2615:9:6"}, {"kind": "number", "nativeSrc": "2626:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2626:2:6", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "2611:3:6", "nodeType": "YulIdentifier", "src": "2611:3:6"}, "nativeSrc": "2611:18:6", "nodeType": "YulFunctionCall", "src": "2611:18:6"}, {"hexValue": "416d6f756e74206d7573742062652067726561746572207468616e2030", "kind": "string", "nativeSrc": "2631:31:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2631:31:6", "type": "", "value": "Amount must be greater than 0"}], "functionName": {"name": "mstore", "nativeSrc": "2604:6:6", "nodeType": "YulIdentifier", "src": "2604:6:6"}, "nativeSrc": "2604:59:6", "nodeType": "YulFunctionCall", "src": "2604:59:6"}, "nativeSrc": "2604:59:6", "nodeType": "YulExpressionStatement", "src": "2604:59:6"}, {"nativeSrc": "2672:26:6", "nodeType": "YulAssignment", "src": "2672:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "2684:9:6", "nodeType": "YulIdentifier", "src": "2684:9:6"}, {"kind": "number", "nativeSrc": "2695:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2695:2:6", "type": "", "value": "96"}], "functionName": {"name": "add", "nativeSrc": "2680:3:6", "nodeType": "YulIdentifier", "src": "2680:3:6"}, "nativeSrc": "2680:18:6", "nodeType": "YulFunctionCall", "src": "2680:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "2672:4:6", "nodeType": "YulIdentifier", "src": "2672:4:6"}]}]}, "name": "abi_encode_tuple_t_stringliteral_3e76f273c719bb7d23db533a2dc9a822ae7d899fcd42eb8910272e24764e8296__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "2351:353:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "2502:9:6", "nodeType": "YulTypedName", "src": "2502:9:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "2516:4:6", "nodeType": "YulTypedName", "src": "2516:4:6", "type": ""}], "src": "2351:353:6"}, {"body": {"nativeSrc": "2883:229:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2883:229:6", "statements": [{"expression": {"arguments": [{"name": "headStart", "nativeSrc": "2900:9:6", "nodeType": "YulIdentifier", "src": "2900:9:6"}, {"kind": "number", "nativeSrc": "2911:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2911:2:6", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nativeSrc": "2893:6:6", "nodeType": "YulIdentifier", "src": "2893:6:6"}, "nativeSrc": "2893:21:6", "nodeType": "YulFunctionCall", "src": "2893:21:6"}, "nativeSrc": "2893:21:6", "nodeType": "YulExpressionStatement", "src": "2893:21:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2934:9:6", "nodeType": "YulIdentifier", "src": "2934:9:6"}, {"kind": "number", "nativeSrc": "2945:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2945:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2930:3:6", "nodeType": "YulIdentifier", "src": "2930:3:6"}, "nativeSrc": "2930:18:6", "nodeType": "YulFunctionCall", "src": "2930:18:6"}, {"kind": "number", "nativeSrc": "2950:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2950:2:6", "type": "", "value": "39"}], "functionName": {"name": "mstore", "nativeSrc": "2923:6:6", "nodeType": "YulIdentifier", "src": "2923:6:6"}, "nativeSrc": "2923:30:6", "nodeType": "YulFunctionCall", "src": "2923:30:6"}, "nativeSrc": "2923:30:6", "nodeType": "YulExpressionStatement", "src": "2923:30:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2973:9:6", "nodeType": "YulIdentifier", "src": "2973:9:6"}, {"kind": "number", "nativeSrc": "2984:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2984:2:6", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "2969:3:6", "nodeType": "YulIdentifier", "src": "2969:3:6"}, "nativeSrc": "2969:18:6", "nodeType": "YulFunctionCall", "src": "2969:18:6"}, {"hexValue": "4d696e20616d6f756e74206d757374206265206c657373207468616e206d6178", "kind": "string", "nativeSrc": "2989:34:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2989:34:6", "type": "", "value": "Min amount must be less than max"}], "functionName": {"name": "mstore", "nativeSrc": "2962:6:6", "nodeType": "YulIdentifier", "src": "2962:6:6"}, "nativeSrc": "2962:62:6", "nodeType": "YulFunctionCall", "src": "2962:62:6"}, "nativeSrc": "2962:62:6", "nodeType": "YulExpressionStatement", "src": "2962:62:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3044:9:6", "nodeType": "YulIdentifier", "src": "3044:9:6"}, {"kind": "number", "nativeSrc": "3055:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3055:2:6", "type": "", "value": "96"}], "functionName": {"name": "add", "nativeSrc": "3040:3:6", "nodeType": "YulIdentifier", "src": "3040:3:6"}, "nativeSrc": "3040:18:6", "nodeType": "YulFunctionCall", "src": "3040:18:6"}, {"hexValue": "20616d6f756e74", "kind": "string", "nativeSrc": "3060:9:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3060:9:6", "type": "", "value": " amount"}], "functionName": {"name": "mstore", "nativeSrc": "3033:6:6", "nodeType": "YulIdentifier", "src": "3033:6:6"}, "nativeSrc": "3033:37:6", "nodeType": "YulFunctionCall", "src": "3033:37:6"}, "nativeSrc": "3033:37:6", "nodeType": "YulExpressionStatement", "src": "3033:37:6"}, {"nativeSrc": "3079:27:6", "nodeType": "YulAssignment", "src": "3079:27:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "3091:9:6", "nodeType": "YulIdentifier", "src": "3091:9:6"}, {"kind": "number", "nativeSrc": "3102:3:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3102:3:6", "type": "", "value": "128"}], "functionName": {"name": "add", "nativeSrc": "3087:3:6", "nodeType": "YulIdentifier", "src": "3087:3:6"}, "nativeSrc": "3087:19:6", "nodeType": "YulFunctionCall", "src": "3087:19:6"}, "variableNames": [{"name": "tail", "nativeSrc": "3079:4:6", "nodeType": "YulIdentifier", "src": "3079:4:6"}]}]}, "name": "abi_encode_tuple_t_stringliteral_446e99ffdf2e03d3177d40dd993d55d45602fea049771b76ceb15b1e4a0f2a97__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "2709:403:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "2860:9:6", "nodeType": "YulTypedName", "src": "2860:9:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "2874:4:6", "nodeType": "YulTypedName", "src": "2874:4:6", "type": ""}], "src": "2709:403:6"}, {"body": {"nativeSrc": "3308:14:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3308:14:6", "statements": [{"nativeSrc": "3310:10:6", "nodeType": "YulAssignment", "src": "3310:10:6", "value": {"name": "pos", "nativeSrc": "3317:3:6", "nodeType": "YulIdentifier", "src": "3317:3:6"}, "variableNames": [{"name": "end", "nativeSrc": "3310:3:6", "nodeType": "YulIdentifier", "src": "3310:3:6"}]}]}, "name": "abi_encode_tuple_packed_t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed", "nativeSrc": "3117:205:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "3292:3:6", "nodeType": "YulTypedName", "src": "3292:3:6", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "3300:3:6", "nodeType": "YulTypedName", "src": "3300:3:6", "type": ""}], "src": "3117:205:6"}, {"body": {"nativeSrc": "3501:232:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3501:232:6", "statements": [{"expression": {"arguments": [{"name": "headStart", "nativeSrc": "3518:9:6", "nodeType": "YulIdentifier", "src": "3518:9:6"}, {"kind": "number", "nativeSrc": "3529:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3529:2:6", "type": "", "value": "32"}], "functionName": {"name": "mstore", "nativeSrc": "3511:6:6", "nodeType": "YulIdentifier", "src": "3511:6:6"}, "nativeSrc": "3511:21:6", "nodeType": "YulFunctionCall", "src": "3511:21:6"}, "nativeSrc": "3511:21:6", "nodeType": "YulExpressionStatement", "src": "3511:21:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3552:9:6", "nodeType": "YulIdentifier", "src": "3552:9:6"}, {"kind": "number", "nativeSrc": "3563:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3563:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "3548:3:6", "nodeType": "YulIdentifier", "src": "3548:3:6"}, "nativeSrc": "3548:18:6", "nodeType": "YulFunctionCall", "src": "3548:18:6"}, {"kind": "number", "nativeSrc": "3568:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3568:2:6", "type": "", "value": "42"}], "functionName": {"name": "mstore", "nativeSrc": "3541:6:6", "nodeType": "YulIdentifier", "src": "3541:6:6"}, "nativeSrc": "3541:30:6", "nodeType": "YulFunctionCall", "src": "3541:30:6"}, "nativeSrc": "3541:30:6", "nodeType": "YulExpressionStatement", "src": "3541:30:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3591:9:6", "nodeType": "YulIdentifier", "src": "3591:9:6"}, {"kind": "number", "nativeSrc": "3602:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3602:2:6", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "3587:3:6", "nodeType": "YulIdentifier", "src": "3587:3:6"}, "nativeSrc": "3587:18:6", "nodeType": "YulFunctionCall", "src": "3587:18:6"}, {"hexValue": "4d617820616d6f756e74206d7573742062652067726561746572207468616e20", "kind": "string", "nativeSrc": "3607:34:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3607:34:6", "type": "", "value": "Max amount must be greater than "}], "functionName": {"name": "mstore", "nativeSrc": "3580:6:6", "nodeType": "YulIdentifier", "src": "3580:6:6"}, "nativeSrc": "3580:62:6", "nodeType": "YulFunctionCall", "src": "3580:62:6"}, "nativeSrc": "3580:62:6", "nodeType": "YulExpressionStatement", "src": "3580:62:6"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3662:9:6", "nodeType": "YulIdentifier", "src": "3662:9:6"}, {"kind": "number", "nativeSrc": "3673:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3673:2:6", "type": "", "value": "96"}], "functionName": {"name": "add", "nativeSrc": "3658:3:6", "nodeType": "YulIdentifier", "src": "3658:3:6"}, "nativeSrc": "3658:18:6", "nodeType": "YulFunctionCall", "src": "3658:18:6"}, {"hexValue": "6d696e20616d6f756e74", "kind": "string", "nativeSrc": "3678:12:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3678:12:6", "type": "", "value": "min amount"}], "functionName": {"name": "mstore", "nativeSrc": "3651:6:6", "nodeType": "YulIdentifier", "src": "3651:6:6"}, "nativeSrc": "3651:40:6", "nodeType": "YulFunctionCall", "src": "3651:40:6"}, "nativeSrc": "3651:40:6", "nodeType": "YulExpressionStatement", "src": "3651:40:6"}, {"nativeSrc": "3700:27:6", "nodeType": "YulAssignment", "src": "3700:27:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "3712:9:6", "nodeType": "YulIdentifier", "src": "3712:9:6"}, {"kind": "number", "nativeSrc": "3723:3:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3723:3:6", "type": "", "value": "128"}], "functionName": {"name": "add", "nativeSrc": "3708:3:6", "nodeType": "YulIdentifier", "src": "3708:3:6"}, "nativeSrc": "3708:19:6", "nodeType": "YulFunctionCall", "src": "3708:19:6"}, "variableNames": [{"name": "tail", "nativeSrc": "3700:4:6", "nodeType": "YulIdentifier", "src": "3700:4:6"}]}]}, "name": "abi_encode_tuple_t_stringliteral_caf27fc83f7a2f76ea0e23c346f21e48964232371e524260eef1e330d3edebe4__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "3327:406:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "3478:9:6", "nodeType": "YulTypedName", "src": "3478:9:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "3492:4:6", "nodeType": "YulTypedName", "src": "3492:4:6", "type": ""}], "src": "3327:406:6"}, {"body": {"nativeSrc": "3846:101:6", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3846:101:6", "statements": [{"nativeSrc": "3856:26:6", "nodeType": "YulAssignment", "src": "3856:26:6", "value": {"arguments": [{"name": "headStart", "nativeSrc": "3868:9:6", "nodeType": "YulIdentifier", "src": "3868:9:6"}, {"kind": "number", "nativeSrc": "3879:2:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3879:2:6", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "3864:3:6", "nodeType": "YulIdentifier", "src": "3864:3:6"}, "nativeSrc": "3864:18:6", "nodeType": "YulFunctionCall", "src": "3864:18:6"}, "variableNames": [{"name": "tail", "nativeSrc": "3856:4:6", "nodeType": "YulIdentifier", "src": "3856:4:6"}]}, {"expression": {"arguments": [{"name": "headStart", "nativeSrc": "3898:9:6", "nodeType": "YulIdentifier", "src": "3898:9:6"}, {"arguments": [{"name": "value0", "nativeSrc": "3913:6:6", "nodeType": "YulIdentifier", "src": "3913:6:6"}, {"kind": "number", "nativeSrc": "3921:18:6", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3921:18:6", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "and", "nativeSrc": "3909:3:6", "nodeType": "YulIdentifier", "src": "3909:3:6"}, "nativeSrc": "3909:31:6", "nodeType": "YulFunctionCall", "src": "3909:31:6"}], "functionName": {"name": "mstore", "nativeSrc": "3891:6:6", "nodeType": "YulIdentifier", "src": "3891:6:6"}, "nativeSrc": "3891:50:6", "nodeType": "YulFunctionCall", "src": "3891:50:6"}, "nativeSrc": "3891:50:6", "nodeType": "YulExpressionStatement", "src": "3891:50:6"}]}, "name": "abi_encode_tuple_t_rational_1_by_1__to_t_uint64__fromStack_reversed", "nativeSrc": "3738:209:6", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "3815:9:6", "nodeType": "YulTypedName", "src": "3815:9:6", "type": ""}, {"name": "value0", "nativeSrc": "3826:6:6", "nodeType": "YulTypedName", "src": "3826:6:6", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "3837:4:6", "nodeType": "YulTypedName", "src": "3837:4:6", "type": ""}], "src": "3738:209:6"}]}, "contents": "{\n    { }\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, value0)\n    }\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        value0 := calldataload(headStart)\n    }\n    function validator_revert_address_payable(value)\n    {\n        if iszero(eq(value, and(value, sub(shl(160, 1), 1)))) { revert(0, 0) }\n    }\n    function abi_decode_tuple_t_uint256t_address_payable(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        value0 := calldataload(headStart)\n        let value := calldataload(add(headStart, 32))\n        validator_revert_address_payable(value)\n        value1 := value\n    }\n    function abi_encode_tuple_t_bool__to_t_bool__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, iszero(iszero(value0)))\n    }\n    function abi_encode_tuple_t_uint256_t_uint256_t_uint256__to_t_uint256_t_uint256_t_uint256__fromStack_reversed(headStart, value2, value1, value0) -> tail\n    {\n        tail := add(headStart, 96)\n        mstore(headStart, value0)\n        mstore(add(headStart, 32), value1)\n        mstore(add(headStart, 64), value2)\n    }\n    function abi_encode_tuple_t_address__to_t_address__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, and(value0, sub(shl(160, 1), 1)))\n    }\n    function abi_decode_tuple_t_uint256t_uint256(headStart, dataEnd) -> value0, value1\n    {\n        if slt(sub(dataEnd, headStart), 64) { revert(0, 0) }\n        value0 := calldataload(headStart)\n        value1 := calldataload(add(headStart, 32))\n    }\n    function abi_decode_tuple_t_address(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        let value := calldataload(headStart)\n        validator_revert_address_payable(value)\n        value0 := value\n    }\n    function abi_encode_tuple_t_uint256_t_uint256__to_t_uint256_t_uint256__fromStack_reversed(headStart, value1, value0) -> tail\n    {\n        tail := add(headStart, 64)\n        mstore(headStart, value0)\n        mstore(add(headStart, 32), value1)\n    }\n    function abi_encode_tuple_t_stringliteral_3e76f273c719bb7d23db533a2dc9a822ae7d899fcd42eb8910272e24764e8296__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 29)\n        mstore(add(headStart, 64), \"Amount must be greater than 0\")\n        tail := add(headStart, 96)\n    }\n    function abi_encode_tuple_t_stringliteral_446e99ffdf2e03d3177d40dd993d55d45602fea049771b76ceb15b1e4a0f2a97__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 39)\n        mstore(add(headStart, 64), \"Min amount must be less than max\")\n        mstore(add(headStart, 96), \" amount\")\n        tail := add(headStart, 128)\n    }\n    function abi_encode_tuple_packed_t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed(pos) -> end\n    { end := pos }\n    function abi_encode_tuple_t_stringliteral_caf27fc83f7a2f76ea0e23c346f21e48964232371e524260eef1e330d3edebe4__to_t_string_memory_ptr__fromStack_reversed(headStart) -> tail\n    {\n        mstore(headStart, 32)\n        mstore(add(headStart, 32), 42)\n        mstore(add(headStart, 64), \"Max amount must be greater than \")\n        mstore(add(headStart, 96), \"min amount\")\n        tail := add(headStart, 128)\n    }\n    function abi_encode_tuple_t_rational_1_by_1__to_t_uint64__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, and(value0, 0xffffffffffffffff))\n    }\n}", "id": 6, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT PUSH2 0xEC JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x7CC1F867 GT PUSH2 0x8A JUMPI DUP1 PUSH4 0xC4511C6A GT PUSH2 0x59 JUMPI DUP1 PUSH4 0xC4511C6A EQ PUSH2 0x273 JUMPI DUP1 PUSH4 0xD0E30DB0 EQ PUSH2 0x293 JUMPI DUP1 PUSH4 0xE4A30116 EQ PUSH2 0x29B JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x2BB JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x7CC1F867 EQ PUSH2 0x1D2 JUMPI DUP1 PUSH4 0x8456CB59 EQ PUSH2 0x201 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x216 JUMPI DUP1 PUSH4 0x8ED83271 EQ PUSH2 0x25D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x3F4BA83A GT PUSH2 0xC6 JUMPI DUP1 PUSH4 0x3F4BA83A EQ PUSH2 0x162 JUMPI DUP1 PUSH4 0x5C975ABB EQ PUSH2 0x177 JUMPI DUP1 PUSH4 0x645006CA EQ PUSH2 0x1A7 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x1BD JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP1 PUSH4 0x12065FE0 EQ PUSH2 0x100 JUMPI DUP1 PUSH4 0x2A80CDA3 EQ PUSH2 0x122 JUMPI DUP1 PUSH4 0x2F940C70 EQ PUSH2 0x142 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLDATASIZE PUSH2 0xFB JUMPI PUSH2 0xF9 PUSH2 0x2DB JUMP JUMPDEST STOP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x10C JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP SELFBALANCE JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE PUSH1 0x20 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x12E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x13D CALLDATASIZE PUSH1 0x4 PUSH2 0xB25 JUMP JUMPDEST PUSH2 0x384 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x14E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x15D CALLDATASIZE PUSH1 0x4 PUSH2 0xB53 JUMP JUMPDEST PUSH2 0x488 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x16E JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x594 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x183 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1B3 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x10F PUSH1 0x0 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1C9 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x5A6 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x1DE JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 SLOAD PUSH1 0x1 SLOAD SELFBALANCE PUSH1 0x40 DUP1 MLOAD SWAP4 DUP5 MSTORE PUSH1 0x20 DUP5 ADD SWAP3 SWAP1 SWAP3 MSTORE SWAP1 DUP3 ADD MSTORE PUSH1 0x60 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x20D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x5B8 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x222 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 SLOAD PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x119 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x269 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x10F PUSH1 0x1 SLOAD DUP2 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x27F JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x28E CALLDATASIZE PUSH1 0x4 PUSH2 0xB25 JUMP JUMPDEST PUSH2 0x5C8 JUMP JUMPDEST PUSH2 0xF9 PUSH2 0x672 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2A7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x2B6 CALLDATASIZE PUSH1 0x4 PUSH2 0xB83 JUMP JUMPDEST PUSH2 0x6B3 JUMP JUMPDEST CALLVALUE DUP1 ISZERO PUSH2 0x2C7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xF9 PUSH2 0x2D6 CALLDATASIZE PUSH1 0x4 PUSH2 0xBA5 JUMP JUMPDEST PUSH2 0x81F JUMP JUMPDEST CALLVALUE PUSH1 0x0 DUP2 SWAP1 SUB PUSH2 0x2FE JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 SLOAD DUP2 LT ISZERO PUSH2 0x321 JUMPI PUSH1 0x40 MLOAD PUSH4 0x617AB12D PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 SLOAD DUP2 GT ISZERO PUSH2 0x344 JUMPI PUSH1 0x40 MLOAD PUSH4 0x6250401 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE TIMESTAMP PUSH1 0x20 DUP3 ADD MSTORE CALLER SWAP2 DUP3 SWAP2 PUSH32 0x90890809C654F11D6E72A28FA60149770A0D11EC6C92319D6CEB2BB0A4EA1A15 SWAP2 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP JUMP JUMPDEST PUSH2 0x38C PUSH2 0x85D JUMP JUMPDEST PUSH1 0x0 DUP2 GT PUSH2 0x3E1 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1D PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x416D6F756E74206D7573742062652067726561746572207468616E2030000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 SLOAD DUP2 LT PUSH2 0x442 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x27 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4D696E20616D6F756E74206D757374206265206C657373207468616E206D6178 PUSH1 0x44 DUP3 ADD MSTORE PUSH7 0x8185B5BDD5B9D PUSH1 0xCA SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH1 0x0 DUP1 SLOAD SWAP1 DUP3 SWAP1 SSTORE PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 SWAP1 MSTORE PUSH32 0x5FB4589FCDFAB8BD40D9776ABC10876BB1CB02C0EDAB28D05CC42869B40E0329 SWAP2 ADD JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP POP JUMP JUMPDEST PUSH2 0x490 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x4B7 JUMPI PUSH1 0x40 MLOAD PUSH4 0xE6C4247B PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST SELFBALANCE DUP3 GT ISZERO PUSH2 0x4D8 JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP4 PUSH1 0x40 MLOAD PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 DUP8 GAS CALL SWAP3 POP POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x525 JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x52A JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP POP SWAP1 POP DUP1 PUSH2 0x54C JUMPI PUSH1 0x40 MLOAD PUSH4 0x12171D83 PUSH1 0xE3 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0xBB43D2E6A13F52D71A3A836CF197FA240FB4EF40914B6E13CC966FD6DFB19B68 DUP5 PUSH1 0x40 MLOAD PUSH2 0x587 SWAP2 DUP2 MSTORE PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG2 POP POP POP JUMP JUMPDEST PUSH2 0x59C PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0x8B8 JUMP JUMPDEST JUMP JUMPDEST PUSH2 0x5AE PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH1 0x0 PUSH2 0x918 JUMP JUMPDEST PUSH2 0x5C0 PUSH2 0x85D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0x989 JUMP JUMPDEST PUSH2 0x5D0 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x0 SLOAD DUP2 GT PUSH2 0x634 JUMPI PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x2A PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x4D617820616D6F756E74206D7573742062652067726561746572207468616E20 PUSH1 0x44 DUP3 ADD MSTORE PUSH10 0x1B5A5B88185B5BDD5B9D PUSH1 0xB2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH1 0x1 DUP1 SLOAD SWAP1 DUP3 SWAP1 SSTORE PUSH1 0x40 DUP1 MLOAD DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP5 SWAP1 MSTORE PUSH32 0x2BB9E3FC7B14157E1812FDBE23016F0756B7EBB061C80589DEC1550779776C78 SWAP2 ADD PUSH2 0x47C JUMP JUMPDEST PUSH2 0x67A PUSH2 0x9D2 JUMP JUMPDEST PUSH2 0x682 PUSH2 0xA1C JUMP JUMPDEST PUSH2 0x68A PUSH2 0x2DB JUMP JUMPDEST PUSH2 0x5A4 PUSH1 0x1 PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 SSTORE JUMP JUMPDEST PUSH32 0xF0C57E16840DF040F15088DC2F81FE391C3923BEC73E23A9662EFC9C229C6A00 DUP1 SLOAD PUSH1 0x1 PUSH1 0x40 SHL DUP2 DIV PUSH1 0xFF AND ISZERO SWAP1 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH1 0x0 DUP2 ISZERO DUP1 ISZERO PUSH2 0x6F9 JUMPI POP DUP3 JUMPDEST SWAP1 POP PUSH1 0x0 DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH1 0x1 EQ DUP1 ISZERO PUSH2 0x716 JUMPI POP ADDRESS EXTCODESIZE ISZERO JUMPDEST SWAP1 POP DUP2 ISZERO DUP1 ISZERO PUSH2 0x724 JUMPI POP DUP1 ISZERO JUMPDEST ISZERO PUSH2 0x742 JUMPI PUSH1 0x40 MLOAD PUSH4 0xF92EE8A9 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP5 SLOAD PUSH8 0xFFFFFFFFFFFFFFFF NOT AND PUSH1 0x1 OR DUP6 SSTORE DUP4 ISZERO PUSH2 0x76C JUMPI DUP5 SLOAD PUSH1 0xFF PUSH1 0x40 SHL NOT AND PUSH1 0x1 PUSH1 0x40 SHL OR DUP6 SSTORE JUMPDEST DUP7 PUSH1 0x0 SUB PUSH2 0x78D JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST DUP7 DUP7 GT PUSH2 0x7AD JUMPI PUSH1 0x40 MLOAD PUSH4 0x162908E3 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0x7B5 PUSH2 0xA73 JUMP JUMPDEST PUSH2 0x7BE CALLER PUSH2 0xA83 JUMP JUMPDEST PUSH2 0x7C6 PUSH2 0xA94 JUMP JUMPDEST PUSH1 0x0 DUP8 SWAP1 SSTORE PUSH1 0x1 DUP7 SWAP1 SSTORE DUP4 ISZERO PUSH2 0x816 JUMPI DUP5 SLOAD PUSH1 0xFF PUSH1 0x40 SHL NOT AND DUP6 SSTORE PUSH1 0x40 MLOAD PUSH1 0x1 DUP2 MSTORE PUSH32 0xC7F505B2F371AE2175EE4913F4499E1F2633A7B5936321EED1CDAEB6115181D2 SWAP1 PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 JUMPDEST POP POP POP POP POP POP POP JUMP JUMPDEST PUSH2 0x827 PUSH2 0x85D JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH2 0x851 JUMPI PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH2 0x85A DUP2 PUSH2 0x918 JUMP JUMPDEST POP JUMP JUMPDEST CALLER PUSH2 0x88F PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND EQ PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 ADD PUSH2 0x3D8 JUMP JUMPDEST PUSH2 0x8C0 PUSH2 0xA9C JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE DUP1 SLOAD PUSH1 0xFF NOT AND DUP2 SSTORE PUSH32 0x5DB9EE0A495BF2E6FF9C91A7834C1BA4FDD244A5E8AA4E537BD38AEAE4B073AA CALLER JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 LOG1 POP JUMP JUMPDEST PUSH32 0x9016D09D72D40FDAE2FD8CEAC6B6234C7706214FD39C1CD1E609A0528C199300 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP5 DUP2 AND SWAP2 DUP3 OR DUP5 SSTORE PUSH1 0x40 MLOAD SWAP3 AND SWAP2 DUP3 SWAP1 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP1 PUSH1 0x0 SWAP1 LOG3 POP POP POP JUMP JUMPDEST PUSH2 0x991 PUSH2 0xA1C JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR DUP2 SSTORE PUSH32 0x62E78CEA01BEE320CD4E420270B5EA74000D11B0C9F74754EBDBFC544B05A258 CALLER PUSH2 0x8FA JUMP JUMPDEST PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 DUP1 SLOAD PUSH1 0x1 NOT ADD PUSH2 0xA16 JUMPI PUSH1 0x40 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x2 SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND ISZERO PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0xD93C0665 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x1 PUSH32 0x9B779B17422D0DF92223018B32B4D1FA46E071723D6817E2486D003BECC55F00 SSTORE JUMP JUMPDEST PUSH2 0xA7B PUSH2 0xACC JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0xB15 JUMP JUMPDEST PUSH2 0xA8B PUSH2 0xACC JUMP JUMPDEST PUSH2 0x85A DUP2 PUSH2 0xB1D JUMP JUMPDEST PUSH2 0x5A4 PUSH2 0xACC JUMP JUMPDEST PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH2 0xBCA DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE SLOAD PUSH1 0xFF AND PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x8DFC202B PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH32 0xF0C57E16840DF040F15088DC2F81FE391C3923BEC73E23A9662EFC9C229C6A00 SLOAD PUSH1 0x1 PUSH1 0x40 SHL SWAP1 DIV PUSH1 0xFF AND PUSH2 0x5A4 JUMPI PUSH1 0x40 MLOAD PUSH4 0x1AFCD79F PUSH1 0xE3 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH2 0xA4D PUSH2 0xACC JUMP JUMPDEST PUSH2 0x827 PUSH2 0xACC JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xB37 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP CALLDATALOAD SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 AND DUP2 EQ PUSH2 0x85A JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xB66 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP3 CALLDATALOAD SWAP2 POP PUSH1 0x20 DUP4 ADD CALLDATALOAD PUSH2 0xB78 DUP2 PUSH2 0xB3E JUMP JUMPDEST DUP1 SWAP2 POP POP SWAP3 POP SWAP3 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x40 DUP4 DUP6 SUB SLT ISZERO PUSH2 0xB96 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP POP DUP1 CALLDATALOAD SWAP3 PUSH1 0x20 SWAP1 SWAP2 ADD CALLDATALOAD SWAP2 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xBB7 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST DUP2 CALLDATALOAD PUSH2 0xBC2 DUP2 PUSH2 0xB3E JUMP JUMPDEST SWAP4 SWAP3 POP POP POP JUMP INVALID 0xCD 0x5E 0xD1 0x5C PUSH15 0x187E77E9AEE88184C21F4F2182AB58 0x27 0xCB EXTCODESIZE PUSH31 0x7FBEDCD63F03300A26469706673582212207AF51485474667EA91105C564C CALLER 0xBA LOG0 LOG3 0x28 0xBA OR 0xE3 0xED SWAP11 0x1F 0xCE SWAP14 0xF9 0x2B 0xEB CALLVALUE 0x4C 0xD6 PUSH5 0x736F6C6343 STOP ADDMOD AND STOP CALLER ", "sourceMap": "580:4795:5:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2390:17;:15;:17::i;:::-;580:4795;;;;;4802:99;;;;;;;;;;-1:-1:-1;4873:21:5;4802:99;;;160:25:6;;;148:2;133:18;4802:99:5;;;;;;;;3001:431;;;;;;;;;;-1:-1:-1;3001:431:5;;;;;:::i;:::-;;:::i;4321:382::-;;;;;;;;;;-1:-1:-1;4321:382:5;;;;;:::i;:::-;;:::i;4118:65::-;;;;;;;;;;;;;:::i;2496:145:3:-;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;2625:9:3;;;2496:145;;1026:14:6;;1019:22;1001:41;;989:2;974:18;2496:145:3;861:187:6;743:31:5;;;;;;;;;;;;;;;;3155:101:0;;;;;;;;;;;;;:::i;5101:272:5:-;;;;;;;;;;-1:-1:-1;5160:17:5;5275:16;5305;;5335:21;5101:272;;;1255:25:6;;;1311:2;1296:18;;1289:34;;;;1339:18;;;1332:34;1243:2;1228:18;5101:272:5;1053:319:6;3992:61:5;;;;;;;;;;;;;:::i;2441:144:0:-;;;;;;;;;;-1:-1:-1;1313:22:0;2570:8;2441:144;;-1:-1:-1;;;;;2570:8:0;;;1523:51:6;;1511:2;1496:18;2441:144:0;1377:203:6;807:31:5;;;;;;;;;;;;;;;;3566:361;;;;;;;;;;-1:-1:-1;3566:361:5;;;;;:::i;:::-;;:::i;2191:97::-;;;:::i;1623:449::-;;;;;;;;;;-1:-1:-1;1623:449:5;;;;;:::i;:::-;;:::i;3405:215:0:-;;;;;;;;;;-1:-1:-1;3405:215:0;;;;;:::i;:::-;;:::i;2473:392:5:-;2536:9;2519:14;2560:11;;;2556:39;;2580:15;;-1:-1:-1;;;2580:15:5;;;;;;;;;;;2556:39;2618:16;;2609:6;:25;2605:54;;;2643:16;;-1:-1:-1;;;2643:16:5;;;;;;;;;;;2605:54;2682:16;;2673:6;:25;2669:54;;;2707:16;;-1:-1:-1;;;2707:16:5;;;;;;;;;;;2669:54;2820:38;;;2272:25:6;;;2842:15:5;2328:2:6;2313:18;;2306:34;2749:10:5;;;;2820:38;;2245:18:6;2820:38:5;;;;;;;2509:356;;2473:392::o;3001:431::-;2334:13:0;:11;:13::i;:::-;3114:1:5::1;3094:17;:21;3086:63;;;::::0;-1:-1:-1;;;3086:63:5;;2553:2:6;3086:63:5::1;::::0;::::1;2535:21:6::0;2592:2;2572:18;;;2565:30;2631:31;2611:18;;;2604:59;2680:18;;3086:63:5::1;;;;;;;;;3187:16;;3167:17;:36;3159:88;;;::::0;-1:-1:-1;;;3159:88:5;;2911:2:6;3159:88:5::1;::::0;::::1;2893:21:6::0;2950:2;2930:18;;;2923:30;2989:34;2969:18;;;2962:62;-1:-1:-1;;;3040:18:6;;;3033:37;3087:19;;3159:88:5::1;2709:403:6::0;3159:88:5::1;3266:17;3286:16:::0;;3312:36;;;;3372:53:::1;::::0;;2272:25:6;;;2328:2;2313:18;;2306:34;;;3372:53:5::1;::::0;2245:18:6;3372:53:5::1;;;;;;;;3076:356;3001:431:::0;:::o;4321:382::-;2334:13:0;:11;:13::i;:::-;-1:-1:-1;;;;;4439:16:5;::::1;4435:45;;4464:16;;-1:-1:-1::0;;;4464:16:5::1;;;;;;;;;;;4435:45;4503:21;4494:6;:30;4490:58;;;4533:15;;-1:-1:-1::0;;;4533:15:5::1;;;;;;;;;;;4490:58;4560:12;4578:2;-1:-1:-1::0;;;;;4578:7:5::1;4593:6;4578:26;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4559:45;;;4619:7;4614:37;;4635:16;;-1:-1:-1::0;;;4635:16:5::1;;;;;;;;;;;4614:37;4693:2;-1:-1:-1::0;;;;;4667:29:5::1;;4685:6;4667:29;;;;160:25:6::0;;148:2;133:18;;14:177;4667:29:5::1;;;;;;;;4425:278;4321:382:::0;;:::o;4118:65::-;2334:13:0;:11;:13::i;:::-;4166:10:5::1;:8;:10::i;:::-;4118:65::o:0;3155:101:0:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;3992:61:5:-:0;2334:13:0;:11;:13::i;:::-;4038:8:5::1;:6;:8::i;3566:361::-:0;2334:13:0;:11;:13::i;:::-;3679:16:5::1;;3659:17;:36;3651:91;;;::::0;-1:-1:-1;;;3651:91:5;;3529:2:6;3651:91:5::1;::::0;::::1;3511:21:6::0;3568:2;3548:18;;;3541:30;3607:34;3587:18;;;3580:62;-1:-1:-1;;;3658:18:6;;;3651:40;3708:19;;3651:91:5::1;3327:406:6::0;3651:91:5::1;3781:16;::::0;;3807:36;;;;3867:53:::1;::::0;;2272:25:6;;;2328:2;2313:18;;2306:34;;;3867:53:5::1;::::0;2245:18:6;3867:53:5::1;2098:248:6::0;2191:97:5;3395:21:4;:19;:21::i;:::-;1979:19:3::1;:17;:19::i;:::-;2264:17:5::2;:15;:17::i;:::-;3437:20:4::0;1949:1;2532:30;4113:23;3860:283;1623:449:5;3147:66:1;4302:15;;-1:-1:-1;;;4302:15:1;;;;4301:16;;4348:14;;4158:30;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:1;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:1;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:1;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:1;-1:-1:-1;;;5011:22:1;;;4977:67;1752:17:5::1;1773:1;1752:22:::0;1748:50:::1;;1783:15;;-1:-1:-1::0;;;1783:15:5::1;;;;;;;;;;;1748:50;1833:17;1812;:38;1808:66;;1859:15;;-1:-1:-1::0;;;1859:15:5::1;;;;;;;;;;;1808:66;1885:24;:22;:24::i;:::-;1919:26;1934:10;1919:14;:26::i;:::-;1955:17;:15;:17::i;:::-;1983:16;:36:::0;;;2029:16:::1;:36:::0;;;5064:101:1;;;;5098:23;;-1:-1:-1;;;;5098:23:1;;;5140:14;;-1:-1:-1;3891:50:6;;5140:14:1;;3879:2:6;3864:18;5140:14:1;;;;;;;5064:101;4092:1079;;;;;1623:449:5;;:::o;3405:215:0:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:0;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:0;;3562:1:::1;3534:31;::::0;::::1;1523:51:6::0;1496:18;;3534:31:0::1;1377:203:6::0;3485:91:0::1;3585:28;3604:8;3585:18;:28::i;:::-;3405:215:::0;:::o;2658:162::-;966:10:2;2717:7:0;1313:22;2570:8;-1:-1:-1;;;;;2570:8:0;;2441:144;2717:7;-1:-1:-1;;;;;2717:23:0;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:0;;966:10:2;2763:40:0;;;1523:51:6;1496:18;;2763:40:0;1377:203:6;3478:178:3;2226:16;:14;:16::i;:::-;-1:-1:-1;;;;;;;;;;;3595:17:3;;-1:-1:-1;;3595:17:3::1;::::0;;3627:22:::1;966:10:2::0;3636:12:3::1;3627:22;::::0;-1:-1:-1;;;;;1541:32:6;;;1523:51;;1511:2;1496:18;3627:22:3::1;;;;;;;3526:130;3478:178::o:0;3774:248:0:-;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:0;;-1:-1:-1;;;;;3941:19:0;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;3170:176:3:-;1979:19;:17;:19::i;:::-;-1:-1:-1;;;;;;;;;;;3288:16:3;;-1:-1:-1;;3288:16:3::1;3300:4;3288:16;::::0;;3319:20:::1;966:10:2::0;3326:12:3::1;887:96:2::0;3470:384:4;2532:30;3670:9;;-1:-1:-1;;3670:20:4;3666:88;;3713:30;;-1:-1:-1;;;3713:30:4;;;;;;;;;;;3666:88;1991:1;3828:19;;3470:384::o;2709:128:3:-;-1:-1:-1;;;;;;;;;;;2625:9:3;;;2770:61;;;2805:15;;-1:-1:-1;;;2805:15:3;;;;;;;;;;;3860:283:4;1949:1;2532:30;4113:23;3860:283::o;2684:111::-;6929:20:1;:18;:20::i;:::-;2754:34:4::1;:32;:34::i;1847:127:0:-:0;6929:20:1;:18;:20::i;:::-;1929:38:0::1;1954:12;1929:24;:38::i;2266:60:3:-:0;6929:20:1;:18;:20::i;2909:126:3:-;-1:-1:-1;;;;;;;;;;;2625:9:3;;;2967:62;;3003:15;;-1:-1:-1;;;3003:15:3;;;;;;;;;;;7082:141:1;3147:66;8558:40;-1:-1:-1;;;8558:40:1;;;;7144:73;;7189:17;;-1:-1:-1;;;7189:17:1;;;;;;;;;;;2801:183:4;6929:20:1;:18;:20::i;1980:235:0:-;6929:20:1;:18;:20::i;196:180:6:-;255:6;308:2;296:9;287:7;283:23;279:32;276:52;;;324:1;321;314:12;276:52;-1:-1:-1;347:23:6;;196:180;-1:-1:-1;196:180:6:o;381:139::-;-1:-1:-1;;;;;464:31:6;;454:42;;444:70;;510:1;507;500:12;525:331;601:6;609;662:2;650:9;641:7;637:23;633:32;630:52;;;678:1;675;668:12;630:52;714:9;701:23;691:33;;774:2;763:9;759:18;746:32;787:39;820:5;787:39;:::i;:::-;845:5;835:15;;;525:331;;;;;:::o;1585:248::-;1653:6;1661;1714:2;1702:9;1693:7;1689:23;1685:32;1682:52;;;1730:1;1727;1720:12;1682:52;-1:-1:-1;;1753:23:6;;;1823:2;1808:18;;;1795:32;;-1:-1:-1;1585:248:6:o;1838:255::-;1897:6;1950:2;1938:9;1929:7;1925:23;1921:32;1918:52;;;1966:1;1963;1956:12;1918:52;2005:9;1992:23;2024:39;2057:5;2024:39;:::i;:::-;2082:5;1838:255;-1:-1:-1;;;1838:255:6:o"}, "methodIdentifiers": {"deposit()": "d0e30db0", "emergencyWithdraw(uint256,address)": "2f940c70", "getBalance()": "12065fe0", "getContractInfo()": "7cc1f867", "initialize(uint256,uint256)": "e4a30116", "maxDepositAmount()": "8ed83271", "minDepositAmount()": "645006ca", "owner()": "8da5cb5b", "pause()": "8456cb59", "paused()": "5c975abb", "renounceOwnership()": "715018a6", "setMaxDepositAmount(uint256)": "c4511c6a", "setMinDepositAmount(uint256)": "2a80cda3", "transferOwnership(address)": "f2fde38b", "unpause()": "3f4ba83a"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AmountTooLarge\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmountTooSmall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TransferFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"EmergencyWithdraw\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newAmount\",\"type\":\"uint256\"}],\"name\":\"MaxDepositAmountUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"oldAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newAmount\",\"type\":\"uint256\"}],\"name\":\"MinDepositAmountUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address payable\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getContractInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"minAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"contractBalance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_minDepositAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_maxDepositAmount\",\"type\":\"uint256\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxDepositAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minDepositAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_maxDepositAmount\",\"type\":\"uint256\"}],\"name\":\"setMaxDepositAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_minDepositAmount\",\"type\":\"uint256\"}],\"name\":\"setMinDepositAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"\\u7528\\u4e8e\\u5904\\u7406PHRS\\u539f\\u751f\\u5e01\\u5145\\u503c\\u5230\\u6e38\\u620f\\u7cfb\\u7edf\\u7684\\u53ef\\u5347\\u7ea7\\u667a\\u80fd\\u5408\\u7ea6\",\"errors\":{\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor\"},\"deposit()\":{\"details\":\"\\u5145\\u503cPHRS\\u539f\\u751f\\u5e01\"},\"emergencyWithdraw(uint256,address)\":{\"details\":\"\\u7d27\\u6025\\u63d0\\u53d6\\u539f\\u751f\\u5e01\\uff08\\u4ec5\\u7ba1\\u7406\\u5458\\uff09\",\"params\":{\"amount\":\"\\u63d0\\u53d6\\u91d1\\u989d\",\"to\":\"\\u63a5\\u6536\\u5730\\u5740\"}},\"getBalance()\":{\"details\":\"\\u83b7\\u53d6\\u5408\\u7ea6\\u539f\\u751f\\u5e01\\u4f59\\u989d\",\"returns\":{\"_0\":\"\\u5408\\u7ea6\\u539f\\u751f\\u5e01\\u4f59\\u989d\"}},\"getContractInfo()\":{\"details\":\"\\u83b7\\u53d6\\u5408\\u7ea6\\u57fa\\u672c\\u4fe1\\u606f\",\"returns\":{\"contractBalance\":\"\\u5408\\u7ea6\\u539f\\u751f\\u5e01\\u4f59\\u989d\",\"maxAmount\":\"\\u6700\\u5927\\u5145\\u503c\\u91d1\\u989d\",\"minAmount\":\"\\u6700\\u5c0f\\u5145\\u503c\\u91d1\\u989d\"}},\"initialize(uint256,uint256)\":{\"details\":\"\\u521d\\u59cb\\u5316\\u51fd\\u6570\\uff0c\\u66ff\\u4ee3\\u6784\\u9020\\u51fd\\u6570\",\"params\":{\"_maxDepositAmount\":\"\\u6700\\u5927\\u5145\\u503c\\u91d1\\u989d\",\"_minDepositAmount\":\"\\u6700\\u5c0f\\u5145\\u503c\\u91d1\\u989d\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"pause()\":{\"details\":\"\\u6682\\u505c\\u5408\\u7ea6\\uff08\\u4ec5\\u7ba1\\u7406\\u5458\\uff09\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setMaxDepositAmount(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u6700\\u5927\\u5145\\u503c\\u91d1\\u989d\\uff08\\u4ec5\\u7ba1\\u7406\\u5458\\uff09\",\"params\":{\"_maxDepositAmount\":\"\\u65b0\\u7684\\u6700\\u5927\\u5145\\u503c\\u91d1\\u989d\"}},\"setMinDepositAmount(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u6700\\u5c0f\\u5145\\u503c\\u91d1\\u989d\\uff08\\u4ec5\\u7ba1\\u7406\\u5458\\uff09\",\"params\":{\"_minDepositAmount\":\"\\u65b0\\u7684\\u6700\\u5c0f\\u5145\\u503c\\u91d1\\u989d\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"unpause()\":{\"details\":\"\\u6062\\u590d\\u5408\\u7ea6\\uff08\\u4ec5\\u7ba1\\u7406\\u5458\\uff09\"}},\"title\":\"PHRS Deposit Contract\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"deposit()\":{\"notice\":\"\\u7528\\u6237\\u53d1\\u9001PHRS\\u539f\\u751f\\u5e01\\u5230\\u6b64\\u51fd\\u6570\\u8fdb\\u884c\\u5145\\u503c\"}},\"notice\":\"\\u652f\\u6301\\u7528\\u6237\\u5c06PHRS\\u539f\\u751f\\u5e01\\u5145\\u503c\\u5230\\u6e38\\u620f\\u8d26\\u6237\\uff0c\\u53d1\\u5c04\\u4e8b\\u4ef6\\u4f9b\\u540e\\u7aef\\u76d1\\u542c\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/PHRSDepositContract.sol\":\"PHRSDepositContract\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol\":{\"keccak256\":\"0xa6bf6b7efe0e6625a9dcd30c5ddf52c4c24fe8372f37c7de9dbf5034746768d5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c353ee3705bbf6fadb84c0fb10ef1b736e8ca3ca1867814349d1487ed207beb\",\"dweb:/ipfs/QmcugaPssrzGGE8q4YZKm2ZhnD3kCijjcgdWWg76nWt3FY\"]},\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"contracts/PHRSDepositContract.sol\":{\"keccak256\":\"0xb545fa66e208b07b419bb5b9d2d1adf59b8c1b0fb511eb986bcb657928273323\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c41d72aa272faa3781906b464095539385f8909f7aede5cc0bcacdadb08437da\",\"dweb:/ipfs/QmdFx7fCFEqS7MhnwDKnCGAvuxuXHLfDV4itoq8LAuhkw8\"]}},\"version\":1}", "storageLayout": {"storage": [{"astId": 814, "contract": "contracts/PHRSDepositContract.sol:PHRSDepositContract", "label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 816, "contract": "contracts/PHRSDepositContract.sol:PHRSDepositContract", "label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256"}], "types": {"t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}}}}}