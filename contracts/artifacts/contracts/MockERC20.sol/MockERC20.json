{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}