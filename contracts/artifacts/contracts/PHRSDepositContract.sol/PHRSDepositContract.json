{"_format": "hh-sol-artifact-1", "contractName": "PHRSDepositContract", "sourceName": "contracts/PHRSDepositContract.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "addedToLegitimate", "type": "bool"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "ForcedDepositHandled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "detectForcedDeposits", "outputs": [{"internalType": "uint256", "name": "forced<PERSON><PERSON>", "type": "uint256"}, {"internalType": "bool", "name": "hasForced", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}, {"internalType": "uint256", "name": "legitimateDeposits", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "action", "type": "bool"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "handleForcedDeposits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalLegitimateDeposits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}