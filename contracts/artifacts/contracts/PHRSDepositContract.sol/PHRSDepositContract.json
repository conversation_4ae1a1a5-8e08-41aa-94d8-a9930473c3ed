{"_format": "hh-sol-artifact-1", "contractName": "PHRSDepositContract", "sourceName": "contracts/PHRSDepositContract.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "addedToLegitimate", "type": "bool"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "ForcedDepositHandled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "detectForcedDeposits", "outputs": [{"internalType": "uint256", "name": "forced<PERSON><PERSON>", "type": "uint256"}, {"internalType": "bool", "name": "hasForced", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}, {"internalType": "uint256", "name": "legitimateDeposits", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "users", "type": "address[]"}], "name": "getUserBalances", "outputs": [{"internalType": "uint256[]", "name": "balances", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "action", "type": "bool"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "handleForcedDeposits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalLegitimateDeposits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}