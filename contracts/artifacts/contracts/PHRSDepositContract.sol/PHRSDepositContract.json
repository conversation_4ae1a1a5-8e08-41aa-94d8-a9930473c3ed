{"_format": "hh-sol-artifact-1", "contractName": "PHRSDepositContract", "sourceName": "contracts/PHRSDepositContract.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "0x6080604052600436106100ec5760003560e01c80637cc1f8671161008a578063c4511c6a11610059578063c4511c6a14610284578063d0e30db0146102a4578063e4a30116146102ac578063f2fde38b146102cc57600080fd5b80637cc1f867146101e35780638456cb59146102125780638da5cb5b146102275780638ed832711461026e57600080fd5b80633f4ba83a116100c65780633f4ba83a146101735780635c975abb14610188578063645006ca146101b8578063715018a6146101ce57600080fd5b806312065fe01461010f5780632a80cda3146101315780632f940c701461015357600080fd5b3661010a5760405163162908e360e11b815260040160405180910390fd5b600080fd5b34801561011b57600080fd5b50475b6040519081526020015b60405180910390f35b34801561013d57600080fd5b5061015161014c366004610b36565b6102ec565b005b34801561015f57600080fd5b5061015161016e366004610b64565b6103f0565b34801561017f57600080fd5b506101516104fc565b34801561019457600080fd5b50600080516020610bdb8339815191525460ff166040519015158152602001610128565b3480156101c457600080fd5b5061011e60005481565b3480156101da57600080fd5b5061015161050e565b3480156101ef57600080fd5b506000546001544760408051938452602084019290925290820152606001610128565b34801561021e57600080fd5b50610151610520565b34801561023357600080fd5b507f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546040516001600160a01b039091168152602001610128565b34801561027a57600080fd5b5061011e60015481565b34801561029057600080fd5b5061015161029f366004610b36565b610530565b6101516105da565b3480156102b857600080fd5b506101516102c7366004610b94565b61061b565b3480156102d857600080fd5b506101516102e7366004610bb6565b610787565b6102f46107c5565b600081116103495760405162461bcd60e51b815260206004820152601d60248201527f416d6f756e74206d7573742062652067726561746572207468616e203000000060448201526064015b60405180910390fd5b60015481106103aa5760405162461bcd60e51b815260206004820152602760248201527f4d696e20616d6f756e74206d757374206265206c657373207468616e206d617860448201526608185b5bdd5b9d60ca1b6064820152608401610340565b600080549082905560408051828152602081018490527f5fb4589fcdfab8bd40d9776abc10876bb1cb02c0edab28d05cc42869b40e032991015b60405180910390a15050565b6103f86107c5565b6001600160a01b03811661041f5760405163e6c4247b60e01b815260040160405180910390fd5b478211156104405760405163162908e360e11b815260040160405180910390fd5b6000816001600160a01b03168360405160006040518083038185875af1925050503d806000811461048d576040519150601f19603f3d011682016040523d82523d6000602084013e610492565b606091505b50509050806104b4576040516312171d8360e31b815260040160405180910390fd5b816001600160a01b03167fbb43d2e6a13f52d71a3a836cf197fa240fb4ef40914b6e13cc966fd6dfb19b68846040516104ef91815260200190565b60405180910390a2505050565b6105046107c5565b61050c610820565b565b6105166107c5565b61050c6000610880565b6105286107c5565b61050c6108f1565b6105386107c5565b600054811161059c5760405162461bcd60e51b815260206004820152602a60248201527f4d617820616d6f756e74206d7573742062652067726561746572207468616e206044820152691b5a5b88185b5bdd5b9d60b21b6064820152608401610340565b600180549082905560408051828152602081018490527f2bb9e3fc7b14157e1812fdbe23016f0756b7ebb061c80589dec1550779776c7891016103e4565b6105e261093a565b6105ea610984565b6105f26109b5565b61050c60017f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f0055565b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a008054600160401b810460ff16159067ffffffffffffffff166000811580156106615750825b905060008267ffffffffffffffff16600114801561067e5750303b155b90508115801561068c575080155b156106aa5760405163f92ee8a960e01b815260040160405180910390fd5b845467ffffffffffffffff1916600117855583156106d457845460ff60401b1916600160401b1785555b866000036106f55760405163162908e360e11b815260040160405180910390fd5b8686116107155760405163162908e360e11b815260040160405180910390fd5b61071d610a84565b61072633610a94565b61072e610aa5565b60008790556001869055831561077e57845460ff60401b19168555604051600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a15b50505050505050565b61078f6107c5565b6001600160a01b0381166107b957604051631e4fbdf760e01b815260006004820152602401610340565b6107c281610880565b50565b336107f77f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b031690565b6001600160a01b03161461050c5760405163118cdaa760e01b8152336004820152602401610340565b610828610aad565b600080516020610bdb833981519152805460ff191681557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a150565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c19930080546001600160a01b031981166001600160a01b03848116918217845560405192169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a3505050565b6108f9610984565b600080516020610bdb833981519152805460ff191660011781557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25833610862565b7f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f0080546001190161097e57604051633ee5aeb560e01b815260040160405180910390fd5b60029055565b600080516020610bdb8339815191525460ff161561050c5760405163d93c066560e01b815260040160405180910390fd5b3460008190036109d85760405163162908e360e11b815260040160405180910390fd5b6000548110156109fb5760405163617ab12d60e11b815260040160405180910390fd5b600154811115610a1e57604051630625040160e01b815260040160405180910390fd5b60408051828152426020820152339182917f90890809c654f11d6e72a28fa60149770a0d11ec6c92319d6ceb2bb0a4ea1a15910160405180910390a25050565b60017f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f0055565b610a8c610add565b61050c610b26565b610a9c610add565b6107c281610b2e565b61050c610add565b600080516020610bdb8339815191525460ff1661050c57604051638dfc202b60e01b815260040160405180910390fd5b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a0054600160401b900460ff1661050c57604051631afcd79f60e31b815260040160405180910390fd5b610a5e610add565b61078f610add565b600060208284031215610b4857600080fd5b5035919050565b6001600160a01b03811681146107c257600080fd5b60008060408385031215610b7757600080fd5b823591506020830135610b8981610b4f565b809150509250929050565b60008060408385031215610ba757600080fd5b50508035926020909101359150565b600060208284031215610bc857600080fd5b8135610bd381610b4f565b939250505056fecd5ed15c6e187e77e9aee88184c21f4f2182ab5827cb3b7e07fbedcd63f03300a26469706673582212205cc9d011e17bbf81c6e749ef31da019b2f1c42dfe6d51cf19bda1c78d3f860c264736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}