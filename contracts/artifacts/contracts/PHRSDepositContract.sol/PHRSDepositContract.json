{"_format": "hh-sol-artifact-1", "contractName": "PHRSDepositContract", "sourceName": "contracts/PHRSDepositContract.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}