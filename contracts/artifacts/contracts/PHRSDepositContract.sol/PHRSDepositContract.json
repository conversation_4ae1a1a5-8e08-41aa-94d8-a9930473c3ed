{"_format": "hh-sol-artifact-1", "contractName": "PHRSDepositContract", "sourceName": "contracts/PHRSDepositContract.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "AmountTooSmall", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}], "name": "EmergencyWithdraw", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MaxDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinDepositAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "maxDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minDepositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDepositAmount", "type": "uint256"}], "name": "setMaxDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minDepositAmount", "type": "uint256"}], "name": "setMinDepositAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}