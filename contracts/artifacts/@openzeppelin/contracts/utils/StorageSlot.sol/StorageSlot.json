{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220c781a8b885a3ca5fe2dc9f4dee2fc882f7984acf1a1b984d9a970a9da7762bad64736f6c63430008160033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220c781a8b885a3ca5fe2dc9f4dee2fc882f7984acf1a1b984d9a970a9da7762bad64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}