{"_format": "hh-sol-artifact-1", "contractName": "Errors", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "abi": [{"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "FailedDeployment", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "MissingPrecompile", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122066d48d2abd48c70ba22ce993ad029194c8ef200202c523b92ffb3b0667c166b564736f6c63430008160033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122066d48d2abd48c70ba22ce993ad029194c8ef200202c523b92ffb3b0667c166b564736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}