{"_format": "hh-sol-artifact-1", "contractName": "Address", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212205965a49a4005a86ed954ab6e57bfedc675ff3c6b372d5d68bd315532662a642c64736f6c63430008160033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212205965a49a4005a86ed954ab6e57bfedc675ff3c6b372d5d68bd315532662a642c64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}