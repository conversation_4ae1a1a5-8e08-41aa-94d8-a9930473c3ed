{"_format": "hh-sol-artifact-1", "contractName": "ERC1967Utils", "sourceName": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "ERC1967InvalidAdmin", "type": "error"}, {"inputs": [{"internalType": "address", "name": "beacon", "type": "address"}], "name": "ERC1967InvalidBeacon", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220e274eaccba5882a0af048d39959ca6e8cdf4933e54498c57fc57a9f23851c56b64736f6c63430008160033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220e274eaccba5882a0af048d39959ca6e8cdf4933e54498c57fc57a9f23851c56b64736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}