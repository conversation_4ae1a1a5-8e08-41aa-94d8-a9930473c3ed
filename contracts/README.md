# PHRS充值智能合约

这是Wolf Fun游戏的PHRS代币充值智能合约，部署在Pharos网络（EVM兼容的L1区块链）上。

## 功能特性

- ✅ 安全的PHRS代币充值功能
- ✅ 充值记录存储和查询
- ✅ 充值事件发射，便于后端监听
- ✅ 重入攻击防护
- ✅ 可暂停功能
- ✅ 管理员权限控制
- ✅ 紧急提取功能
- ✅ 充值金额限制

## 合约架构

### 主要合约
- `PHRSDepositContract.sol` - 主充值合约
- `MockERC20.sol` - 测试用的模拟ERC20代币

### 安全特性
- 使用OpenZeppelin的安全库
- ReentrancyGuard防止重入攻击
- Ownable提供管理员权限控制
- Pausable支持紧急暂停
- SafeERC20确保代币转账安全

## 快速开始

### 1. 安装依赖

```bash
cd contracts
npm install
```

### 2. 编译合约

```bash
npm run compile
```

### 3. 运行测试

```bash
npm run test
```

### 4. 部署合约

#### 配置环境变量

创建 `.env` 文件：

```env
# 私钥（用于部署）
PRIVATE_KEY=your_private_key_here

# PHRS代币合约地址
PHRS_TOKEN_ADDRESS=0x...

# 充值限制
MIN_DEPOSIT_AMOUNT=1      # 最小充值金额（PHRS）
MAX_DEPOSIT_AMOUNT=10000  # 最大充值金额（PHRS）

# Pharos网络配置
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network
PHAROS_MAINNET_RPC_URL=https://rpc.pharos.network

# 区块链浏览器API（用于合约验证）
PHAROS_EXPLORER_API_KEY=your_api_key_here
```

#### 部署到测试网

```bash
npm run deploy:testnet
```

#### 部署到主网

```bash
npm run deploy:mainnet
```

## 合约接口

### 主要函数

#### 用户函数

```solidity
// 充值PHRS代币
function deposit(uint256 amount) external

// 获取用户充值记录数量
function getUserDepositCount(address user) external view returns (uint256)

// 获取用户指定充值记录
function getUserDeposit(address user, uint256 index) external view returns (DepositRecord memory)

// 批量获取用户充值记录
function getUserDeposits(address user, uint256 offset, uint256 limit) external view returns (DepositRecord[] memory)
```

#### 管理员函数

```solidity
// 设置最小充值金额
function setMinDepositAmount(uint256 _minDepositAmount) external onlyOwner

// 设置最大充值金额
function setMaxDepositAmount(uint256 _maxDepositAmount) external onlyOwner

// 暂停合约
function pause() external onlyOwner

// 恢复合约
function unpause() external onlyOwner

// 紧急提取代币
function emergencyWithdraw(address token, uint256 amount, address to) external onlyOwner
```

### 事件

```solidity
// 充值事件
event Deposit(address indexed user, uint256 amount, uint256 timestamp, uint256 indexed depositId);

// 参数更新事件
event MinDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);
event MaxDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);

// 紧急提取事件
event EmergencyWithdraw(address indexed token, uint256 amount, address indexed to);
```

### 数据结构

```solidity
struct DepositRecord {
    address user;           // 用户地址
    uint256 amount;         // 充值金额
    uint256 timestamp;      // 充值时间戳
    uint256 blockNumber;    // 区块号
    bytes32 txHash;         // 交易哈希
}
```

## 使用示例

### JavaScript/TypeScript

```javascript
const { ethers } = require("ethers");

// 连接到Pharos网络
const provider = new ethers.JsonRpcProvider("https://rpc.pharos.network");
const wallet = new ethers.Wallet("your_private_key", provider);

// 合约实例
const contractAddress = "0x..."; // 部署后的合约地址
const contractABI = [...]; // 从artifacts中获取
const contract = new ethers.Contract(contractAddress, contractABI, wallet);

// 充值PHRS代币
async function deposit(amount) {
    // 首先授权代币
    const phrsToken = new ethers.Contract(phrsTokenAddress, erc20ABI, wallet);
    await phrsToken.approve(contractAddress, amount);
    
    // 执行充值
    const tx = await contract.deposit(amount);
    await tx.wait();
    
    console.log("充值成功:", tx.hash);
}

// 监听充值事件
contract.on("Deposit", (user, amount, timestamp, depositId) => {
    console.log("新充值:", {
        user,
        amount: ethers.formatEther(amount),
        timestamp: new Date(timestamp * 1000),
        depositId
    });
});
```

## 安全考虑

1. **重入攻击防护**: 使用OpenZeppelin的ReentrancyGuard
2. **权限控制**: 关键函数仅限管理员调用
3. **暂停机制**: 紧急情况下可暂停合约
4. **输入验证**: 严格验证所有输入参数
5. **代币安全**: 使用SafeERC20进行代币操作

## 测试覆盖

- ✅ 合约部署测试
- ✅ 充值功能测试
- ✅ 权限控制测试
- ✅ 暂停功能测试
- ✅ 查询功能测试
- ✅ 边界条件测试
- ✅ 错误处理测试

## 部署信息

部署完成后，相关信息会保存在 `deployments/` 目录下：

- `{network}.json` - 部署信息
- `PHRSDepositContract-{network}.abi.json` - 合约ABI

## 许可证

MIT License
