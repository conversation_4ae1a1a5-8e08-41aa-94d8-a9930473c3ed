require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    hardhat: {
      chainId: 1337
    },
    pharos_testnet: {
      url: process.env.PHAROS_TESTNET_RPC_URL || "https://testnet-rpc.pharos.network",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 1001, // Pharos测试网链ID，需要根据实际情况调整
      gasPrice: ***********, // 20 gwei
      gas: 8000000
    },
    pharos_mainnet: {
      url: process.env.PHAROS_MAINNET_RPC_URL || "https://rpc.pharos.network",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 1000, // Pharos主网链ID，需要根据实际情况调整
      gasPrice: ***********, // 20 gwei
      gas: 8000000
    }
  },
  etherscan: {
    apiKey: {
      pharos_testnet: process.env.PHAROS_EXPLORER_API_KEY || "your-api-key",
      pharos_mainnet: process.env.PHAROS_EXPLORER_API_KEY || "your-api-key"
    },
    customChains: [
      {
        network: "pharos_testnet",
        chainId: 1001,
        urls: {
          apiURL: process.env.PHAROS_TESTNET_EXPLORER_API || "https://testnet-explorer.pharos.network/api",
          browserURL: process.env.PHAROS_TESTNET_EXPLORER || "https://testnet-explorer.pharos.network"
        }
      },
      {
        network: "pharos_mainnet",
        chainId: 1000,
        urls: {
          apiURL: process.env.PHAROS_MAINNET_EXPLORER_API || "https://explorer.pharos.network/api",
          browserURL: process.env.PHAROS_MAINNET_EXPLORER || "https://explorer.pharos.network"
        }
      }
    ]
  },
  gasReporter: {
    enabled: process.env.REPORT_GAS !== undefined,
    currency: "USD"
  }
};
