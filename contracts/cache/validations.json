{"version": "3.4", "log": [{"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:26", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/PHRSDepositContract.sol:PHRSDepositContract": {"src": "contracts/PHRSDepositContract.sol:17", "version": {"withMetadata": "9ed3759552b6ea7cdbdd619fa78bfa2130182fcb752c4351d0e7fcb5676ab230", "withoutMetadata": "555b395bf30084a4e98c64470ef3a5547ca17c2bf0f4a94fd848a032fc7b054c", "linkedWithoutMetadata": "555b395bf30084a4e98c64470ef3a5547ca17c2bf0f4a94fd848a032fc7b054c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(uint256,uint256)", "deposit()", "()", "()", "setMinDepositAmount(uint256)", "setMaxDepositAmount(uint256)", "pause()", "unpause()", "emergencyWithdraw(uint256,address payable)", "getBalance()", "getContractInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:27"}, {"label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:29"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:26", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/PHRSDepositContract.sol:PHRSDepositContract": {"src": "contracts/PHRSDepositContract.sol:17", "version": {"withMetadata": "16ea20b09f72602da0a24b5b50466b500c5470ea1e997d226d26f7e620b75de3", "withoutMetadata": "f8c2792d7333a586ff95bd0af33eda0222256308f13395a51fc7f608c3d43325", "linkedWithoutMetadata": "f8c2792d7333a586ff95bd0af33eda0222256308f13395a51fc7f608c3d43325"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(uint256,uint256)", "deposit()", "()", "setMinDepositAmount(uint256)", "setMaxDepositAmount(uint256)", "pause()", "unpause()", "emergencyWithdraw(uint256,address payable)", "getBalance()", "getContractInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:27"}, {"label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:29"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:26", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/PHRSDepositContract.sol:PHRSDepositContract": {"src": "contracts/PHRSDepositContract.sol:17", "version": {"withMetadata": "9ed3759552b6ea7cdbdd619fa78bfa2130182fcb752c4351d0e7fcb5676ab230", "withoutMetadata": "555b395bf30084a4e98c64470ef3a5547ca17c2bf0f4a94fd848a032fc7b054c", "linkedWithoutMetadata": "555b395bf30084a4e98c64470ef3a5547ca17c2bf0f4a94fd848a032fc7b054c"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(uint256,uint256)", "deposit()", "()", "()", "setMinDepositAmount(uint256)", "setMaxDepositAmount(uint256)", "pause()", "unpause()", "emergencyWithdraw(uint256,address payable)", "getBalance()", "getContractInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:27"}, {"label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:29"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:26", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "contracts/PHRSDepositContract.sol:PHRSDepositContract": {"src": "contracts/PHRSDepositContract.sol:17", "version": {"withMetadata": "22ecc7087d607b25ed85bc35525bbff086ee3c87812d5b8076064edc678269a3", "withoutMetadata": "8f66ece1610f638366dc95d211d8ac2aeba7386efa980923734d1f342d6d22c5", "linkedWithoutMetadata": "8f66ece1610f638366dc95d211d8ac2aeba7386efa980923734d1f342d6d22c5"}, "inherit": ["@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(uint256,uint256)", "deposit()", "()", "setMinDepositAmount(uint256)", "setMaxDepositAmount(uint256)", "pause()", "unpause()", "emergencyWithdraw(uint256,address payable)", "getBalance()", "getContractInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:27"}, {"label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:29"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)161_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)225_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}, {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:56", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:20", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils"], "methods": ["proxiableUUID()", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin/contracts/utils/Address.sol:97"}], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)224_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:26", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)288_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": false, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/IERC1967.sol:IERC1967": {"src": "@openzeppelin/contracts/interfaces/IERC1967.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable": {"src": "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils": {"src": "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:15", "version": {"withMetadata": "5f9dfe97820bc4a14500f42ea2930441dca798fac39f02a16362b8cd29fc6afa", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin/contracts/utils/Address.sol:97"}, {"kind": "delegatecall", "src": "@openzeppelin/contracts/utils/Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:IBeacon": {"src": "@openzeppelin/contracts/proxy/beacon/IBeacon.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20": {"src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:29", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["name()", "symbol()", "decimals()", "totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:44"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:37"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin/contracts/token/ERC20/IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata": {"src": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:11", "inherit": ["@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20"], "libraries": [], "methods": ["name()", "symbol()", "decimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin/contracts/utils/Address.sol:11", "version": {"withMetadata": "c1f4546bbd850723d69183e656d5a123029205377d830634200ac0c36d9296a8", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Errors.sol:Errors"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin/contracts/utils/Address.sol:97"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin/contracts/utils/Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Errors.sol:Errors": {"src": "@openzeppelin/contracts/utils/Errors.sol:14", "version": {"withMetadata": "5f46a4584c437cd91e6e4386b93037e61fa724904275bd84b5f58da24743a61f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/StorageSlot.sol:StorageSlot": {"src": "@openzeppelin/contracts/utils/StorageSlot.sol:34", "version": {"withMetadata": "e34a460afe63f8e676880ba0751522137768df1fdd3fff06c598d9006111366b", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/MockERC20.sol:MockERC20": {"src": "contracts/MockERC20.sol:10", "version": {"withMetadata": "1eb83d665a3fd2617b151f48c38f4700c270a6ce090b382c0674a0886bbeb425", "withoutMetadata": "5aa9d6c33d0baa57f1d67bae15103b253b02446d4c39dd27cb4923c05d177972", "linkedWithoutMetadata": "5aa9d6c33d0baa57f1d67bae15103b253b02446d4c39dd27cb4923c05d177972"}, "inherit": ["@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(string,string,uint256)", "mint(address,uint256)", "burn(uint256)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "MockERC20", "src": "contracts/MockERC20.sol:12"}], "layout": {"storage": [{"label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:30"}, {"label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:32"}, {"label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:34"}, {"label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:36"}, {"label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage", "contract": "ERC20", "src": "@openzeppelin/contracts/token/ERC20/ERC20.sol:37"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/PHRSDepositContract.sol:PHRSDepositContract": {"src": "contracts/PHRSDepositContract.sol:18", "version": {"withMetadata": "a64e331ecb4ff28ad7c1558bdcdff1a361eb1eaa495612f478c92e7973b17097", "withoutMetadata": "fd82aed8e2b4a36d6ce737130bdb706ead20dcb7b4a7ca24221a6405125dbed2", "linkedWithoutMetadata": "fd82aed8e2b4a36d6ce737130bdb706ead20dcb7b4a7ca24221a6405125dbed2"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts/interfaces/draft-IERC1822.sol:IERC1822Proxiable", "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(uint256,uint256)", "deposit()", "()", "setMinDepositAmount(uint256)", "setMaxDepositAmount(uint256)", "pause()", "unpause()", "emergencyWithdraw(uint256,address payable)", "getBalance()", "getContractInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "minDepositAmount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:29"}, {"label": "maxDepositAmount", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "PHRSDepositContract", "src": "contracts/PHRSDepositContract.sol:31"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)224_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)288_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {"erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}, "solcVersion": "0.8.22"}}]}