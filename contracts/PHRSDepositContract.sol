// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title PHRS Deposit Contract
 * @dev 用于处理PHRS代币充值到游戏系统的智能合约
 * @notice 支持用户将PHRS代币充值到游戏账户，并记录充值历史
 */
contract PHRSDepositContract is ReentrancyGuard, Ownable, Pausable {
    using SafeERC20 for IERC20;

    // PHRS代币合约地址
    IERC20 public immutable phrsToken;
    
    // 最小充值金额
    uint256 public minDepositAmount;
    
    // 最大充值金额
    uint256 public maxDepositAmount;
    
    // 充值记录结构
    struct DepositRecord {
        address user;           // 用户地址
        uint256 amount;         // 充值金额
        uint256 timestamp;      // 充值时间戳
        uint256 blockNumber;    // 区块号
        bytes32 txHash;         // 交易哈希
    }
    
    // 用户充值记录映射
    mapping(address => DepositRecord[]) public userDeposits;
    
    // 用户总充值金额映射
    mapping(address => uint256) public userTotalDeposits;
    
    // 全局充值记录数组
    DepositRecord[] public allDeposits;
    
    // 充值计数器
    uint256 public totalDepositCount;
    
    // 总充值金额
    uint256 public totalDepositAmount;

    // 事件定义
    event Deposit(
        address indexed user,
        uint256 amount,
        uint256 timestamp,
        uint256 indexed depositId
    );
    
    event MinDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);
    event MaxDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);
    event EmergencyWithdraw(address indexed token, uint256 amount, address indexed to);

    // 错误定义
    error InvalidAmount();
    error AmountTooSmall();
    error AmountTooLarge();
    error TransferFailed();
    error InvalidAddress();

    /**
     * @dev 构造函数
     * @param _phrsToken PHRS代币合约地址
     * @param _minDepositAmount 最小充值金额
     * @param _maxDepositAmount 最大充值金额
     */
    constructor(
        address _phrsToken,
        uint256 _minDepositAmount,
        uint256 _maxDepositAmount
    ) {
        if (_phrsToken == address(0)) revert InvalidAddress();
        if (_minDepositAmount == 0) revert InvalidAmount();
        if (_maxDepositAmount <= _minDepositAmount) revert InvalidAmount();
        
        phrsToken = IERC20(_phrsToken);
        minDepositAmount = _minDepositAmount;
        maxDepositAmount = _maxDepositAmount;
    }

    /**
     * @dev 充值PHRS代币
     * @param amount 充值金额
     */
    function deposit(uint256 amount) external nonReentrant whenNotPaused {
        if (amount == 0) revert InvalidAmount();
        if (amount < minDepositAmount) revert AmountTooSmall();
        if (amount > maxDepositAmount) revert AmountTooLarge();

        address user = msg.sender;
        
        // 转移代币到合约
        phrsToken.safeTransferFrom(user, address(this), amount);
        
        // 创建充值记录
        DepositRecord memory record = DepositRecord({
            user: user,
            amount: amount,
            timestamp: block.timestamp,
            blockNumber: block.number,
            txHash: blockhash(block.number - 1) // 使用前一个区块的哈希作为参考
        });
        
        // 更新存储
        userDeposits[user].push(record);
        userTotalDeposits[user] += amount;
        allDeposits.push(record);
        totalDepositCount++;
        totalDepositAmount += amount;
        
        // 发射事件
        emit Deposit(user, amount, block.timestamp, totalDepositCount - 1);
    }

    /**
     * @dev 获取用户充值记录数量
     * @param user 用户地址
     * @return 充值记录数量
     */
    function getUserDepositCount(address user) external view returns (uint256) {
        return userDeposits[user].length;
    }

    /**
     * @dev 获取用户指定索引的充值记录
     * @param user 用户地址
     * @param index 记录索引
     * @return 充值记录
     */
    function getUserDeposit(address user, uint256 index) external view returns (DepositRecord memory) {
        require(index < userDeposits[user].length, "Index out of bounds");
        return userDeposits[user][index];
    }

    /**
     * @dev 批量获取用户充值记录
     * @param user 用户地址
     * @param offset 起始偏移量
     * @param limit 返回数量限制
     * @return records 充值记录数组
     */
    function getUserDeposits(
        address user,
        uint256 offset,
        uint256 limit
    ) external view returns (DepositRecord[] memory records) {
        uint256 userDepositCount = userDeposits[user].length;
        if (offset >= userDepositCount) {
            return new DepositRecord[](0);
        }
        
        uint256 end = offset + limit;
        if (end > userDepositCount) {
            end = userDepositCount;
        }
        
        records = new DepositRecord[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            records[i - offset] = userDeposits[user][i];
        }
    }

    /**
     * @dev 获取全局充值记录
     * @param index 记录索引
     * @return 充值记录
     */
    function getDeposit(uint256 index) external view returns (DepositRecord memory) {
        require(index < allDeposits.length, "Index out of bounds");
        return allDeposits[index];
    }

    /**
     * @dev 批量获取全局充值记录
     * @param offset 起始偏移量
     * @param limit 返回数量限制
     * @return records 充值记录数组
     */
    function getDeposits(
        uint256 offset,
        uint256 limit
    ) external view returns (DepositRecord[] memory records) {
        if (offset >= allDeposits.length) {
            return new DepositRecord[](0);
        }
        
        uint256 end = offset + limit;
        if (end > allDeposits.length) {
            end = allDeposits.length;
        }
        
        records = new DepositRecord[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            records[i - offset] = allDeposits[i];
        }
    }

    /**
     * @dev 设置最小充值金额（仅管理员）
     * @param _minDepositAmount 新的最小充值金额
     */
    function setMinDepositAmount(uint256 _minDepositAmount) external onlyOwner {
        require(_minDepositAmount > 0, "Amount must be greater than 0");
        require(_minDepositAmount < maxDepositAmount, "Min amount must be less than max amount");
        
        uint256 oldAmount = minDepositAmount;
        minDepositAmount = _minDepositAmount;
        
        emit MinDepositAmountUpdated(oldAmount, _minDepositAmount);
    }

    /**
     * @dev 设置最大充值金额（仅管理员）
     * @param _maxDepositAmount 新的最大充值金额
     */
    function setMaxDepositAmount(uint256 _maxDepositAmount) external onlyOwner {
        require(_maxDepositAmount > minDepositAmount, "Max amount must be greater than min amount");
        
        uint256 oldAmount = maxDepositAmount;
        maxDepositAmount = _maxDepositAmount;
        
        emit MaxDepositAmountUpdated(oldAmount, _maxDepositAmount);
    }

    /**
     * @dev 暂停合约（仅管理员）
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约（仅管理员）
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取代币（仅管理员）
     * @param token 代币合约地址
     * @param amount 提取金额
     * @param to 接收地址
     */
    function emergencyWithdraw(
        address token,
        uint256 amount,
        address to
    ) external onlyOwner {
        if (to == address(0)) revert InvalidAddress();
        
        IERC20(token).safeTransfer(to, amount);
        
        emit EmergencyWithdraw(token, amount, to);
    }

    /**
     * @dev 获取合约中指定代币的余额
     * @param token 代币合约地址
     * @return 代币余额
     */
    function getTokenBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    /**
     * @dev 获取合约基本信息
     * @return phrsTokenAddress PHRS代币地址
     * @return minAmount 最小充值金额
     * @return maxAmount 最大充值金额
     * @return totalCount 总充值次数
     * @return totalAmount 总充值金额
     * @return contractBalance 合约PHRS余额
     */
    function getContractInfo() external view returns (
        address phrsTokenAddress,
        uint256 minAmount,
        uint256 maxAmount,
        uint256 totalCount,
        uint256 totalAmount,
        uint256 contractBalance
    ) {
        return (
            address(phrsToken),
            minDepositAmount,
            maxDepositAmount,
            totalDepositCount,
            totalDepositAmount,
            phrsToken.balanceOf(address(this))
        );
    }
}
