// src/tests/helpers/authHelper.ts
import jwt from 'jsonwebtoken';

/**
 * 生成测试用的JWT token
 */
export function generateTestToken(userId: number, walletId: number): string {
  const payload = {
    userId,
    walletId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
  };

  const secret = process.env.JWT_SECRET || 'test-secret';
  return jwt.sign(payload, secret);
}

/**
 * 解析JWT token
 */
export function parseTestToken(token: string): any {
  const secret = process.env.JWT_SECRET || 'test-secret';
  return jwt.verify(token, secret);
}

/**
 * 生成管理员测试token
 */
export function generateAdminTestToken(): string {
  const payload = {
    userId: 1,
    walletId: 1,
    isAdmin: true,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60)
  };

  const secret = process.env.JWT_SECRET || 'test-secret';
  return jwt.sign(payload, secret);
}
