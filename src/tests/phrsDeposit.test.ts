// src/tests/phrsDeposit.test.ts
import request from 'supertest';
import { app } from '../app';
import { sequelize } from '../config/db';
import { User, UserWallet, PhrsDeposit } from '../models';
import { generateTestToken } from './helpers/authHelper';
import { PhrsDepositService } from '../services/phrsDepositService';
import BigNumber from 'bignumber.js';

// Mock ethers for testing
jest.mock('ethers', () => ({
  ethers: {
    JsonRpcProvider: jest.fn().mockImplementation(() => ({
      getBlockNumber: jest.fn().mockResolvedValue(1000),
      _getConnection: jest.fn().mockReturnValue({ url: 'http://test-rpc' })
    })),
    Contract: jest.fn().mockImplementation(() => ({
      on: jest.fn(),
      removeAllListeners: jest.fn(),
      queryFilter: jest.fn().mockResolvedValue([]),
      filters: {
        Deposit: jest.fn().mockReturnValue({})
      }
    })),
    formatEther: jest.fn().mockImplementation((value) => (Number(value) / 1e18).toString()),
    EventLog: class MockEventLog {
      constructor(public args: any[], public transactionHash: string, public blockNumber: number) {}
    }
  }
}));

describe('PHRS Deposit API', () => {
  let testUser: User;
  let testWallet: UserWallet;
  let authToken: string;

  beforeAll(async () => {
    await sequelize.authenticate();
  });

  beforeEach(async () => {
    // 清理测试数据
    await PhrsDeposit.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });

    // 创建测试用户
    testUser = await User.create({
      telegramId: '123456789',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User'
    });

    // 创建测试钱包
    testWallet = await UserWallet.create({
      userId: testUser.id,
      walletAddress: '******************************************',
      phrsBalance: '50.000',
      phrsWalletAddress: '******************************************',
      gem: '0',
      milk: 0
    });

    authToken = generateTestToken(testUser.id, testWallet.id);
  });

  afterEach(async () => {
    await PhrsDeposit.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/phrs-deposit/bind-wallet', () => {
    it('应该成功绑定PHRS钱包地址', async () => {
      const newAddress = '******************************************';
      
      const response = await request(app)
        .post('/api/phrs-deposit/bind-wallet')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          phrsWalletAddress: newAddress
        });

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.phrsWalletAddress).toBe(newAddress.toLowerCase());

      // 验证数据库更新
      await testWallet.reload();
      expect(testWallet.phrsWalletAddress).toBe(newAddress.toLowerCase());
    });

    it('应该拒绝无效的钱包地址', async () => {
      const response = await request(app)
        .post('/api/phrs-deposit/bind-wallet')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          phrsWalletAddress: 'invalid-address'
        });

      expect(response.status).toBe(400);
      expect(response.body.ok).toBe(false);
    });

    it('应该拒绝已被其他用户绑定的地址', async () => {
      // 创建另一个用户和钱包
      const otherUser = await User.create({
        telegramId: '987654321',
        username: 'otheruser',
        firstName: 'Other',
        lastName: 'User'
      });

      const otherWallet = await UserWallet.create({
        userId: otherUser.id,
        walletAddress: '******************************************',
        phrsWalletAddress: '******************************************',
        gem: '0',
        milk: 0
      });

      const response = await request(app)
        .post('/api/phrs-deposit/bind-wallet')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          phrsWalletAddress: '******************************************'
        });

      expect(response.status).toBe(400);
      expect(response.body.ok).toBe(false);
    });
  });

  describe('GET /api/phrs-deposit/deposits', () => {
    beforeEach(async () => {
      // 创建测试充值记录
      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: '100.000',
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: '50.000',
        transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
        blockNumber: 1001,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'PENDING',
        confirmations: 3
      });
    });

    it('应该返回用户的充值记录', async () => {
      const response = await request(app)
        .get('/api/phrs-deposit/deposits')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.deposits).toHaveLength(2);
      expect(response.body.data.pagination.total).toBe(2);
    });

    it('应该支持状态过滤', async () => {
      const response = await request(app)
        .get('/api/phrs-deposit/deposits?status=CONFIRMED')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.deposits).toHaveLength(1);
      expect(response.body.data.deposits[0].status).toBe('CONFIRMED');
    });

    it('应该支持分页', async () => {
      const response = await request(app)
        .get('/api/phrs-deposit/deposits?page=1&limit=1')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.deposits).toHaveLength(1);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.pagination.totalPages).toBe(2);
    });
  });

  describe('POST /api/phrs-deposit/sync-balance', () => {
    it('应该手动同步PHRS余额', async () => {
      // Mock PhrsDepositService.syncUserBalance
      const mockSyncUserBalance = jest.fn().mockResolvedValue(undefined);
      jest.doMock('../services/phrsDepositService', () => ({
        phrsDepositService: {
          syncUserBalance: mockSyncUserBalance
        }
      }));

      const response = await request(app)
        .post('/api/phrs-deposit/sync-balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
    });

    it('应该拒绝未绑定PHRS钱包的用户', async () => {
      // 清除PHRS钱包地址
      await testWallet.update({ phrsWalletAddress: null });

      const response = await request(app)
        .post('/api/phrs-deposit/sync-balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.ok).toBe(false);
    });
  });

  describe('GET /api/phrs-deposit/health', () => {
    it('应该返回服务健康状态', async () => {
      const response = await request(app)
        .get('/api/phrs-deposit/health');

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.service).toBeDefined();
      expect(response.body.data.monitor).toBeDefined();
    });
  });

  describe('PhrsDepositService 单元测试', () => {
    let service: PhrsDepositService;

    beforeEach(() => {
      // Mock environment variables
      process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS = '0xcontract1234567890123456789012345678901234';
      process.env.PHAROS_RPC_URL = 'http://test-rpc';
    });

    it('应该正确初始化服务', () => {
      expect(() => {
        service = new PhrsDepositService();
      }).not.toThrow();
    });

    it('应该正确处理充值事件', async () => {
      service = new PhrsDepositService();
      
      // 创建模拟事件
      const mockEvent = {
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        args: [
          testWallet.phrsWalletAddress, // user
          BigInt('100000000000000000000'), // amount (100 PHRS in wei)
          BigInt(Math.floor(Date.now() / 1000)), // timestamp
          BigInt(0) // depositId
        ]
      };

      // 测试事件处理
      await expect(
        service['handleDepositEvent'](
          mockEvent.args[0],
          mockEvent.args[1],
          mockEvent.args[2],
          mockEvent.args[3],
          mockEvent as any
        )
      ).resolves.not.toThrow();
    });

    it('应该正确同步用户余额', async () => {
      service = new PhrsDepositService();

      // 创建确认的充值记录
      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: '75.000',
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: '25.000',
        transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
        blockNumber: 1001,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      await service.syncUserBalance(testWallet.phrsWalletAddress!);

      // 验证余额更新
      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe('100.000'); // 75 + 25
    });

    it('应该返回正确的服务状态', () => {
      service = new PhrsDepositService();
      const status = service.getStatus();

      expect(status).toHaveProperty('isListening');
      expect(status).toHaveProperty('contractAddress');
      expect(status).toHaveProperty('lastProcessedBlock');
      expect(status).toHaveProperty('providerUrl');
    });
  });

  describe('BigNumber精度测试', () => {
    it('应该正确处理大数值充值', async () => {
      const largeAmount = '999999999999999999999.123';
      
      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: largeAmount,
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      const response = await request(app)
        .get('/api/phrs-deposit/deposits')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.deposits[0].amount).toBe(largeAmount);
    });

    it('应该正确累加多笔充值', async () => {
      const service = new PhrsDepositService();
      
      // 创建多笔充值记录
      const amounts = ['123.456', '789.012', '345.678'];
      for (let i = 0; i < amounts.length; i++) {
        await PhrsDeposit.create({
          walletId: testWallet.id,
          userAddress: testWallet.phrsWalletAddress!,
          amount: amounts[i],
          transactionHash: `0x${i.toString().padStart(64, '0')}`,
          blockNumber: 1000 + i,
          blockTimestamp: new Date(),
          contractAddress: '0xcontract1234567890123456789012345678901234',
          status: 'CONFIRMED',
          confirmations: 12
        });
      }

      await service.syncUserBalance(testWallet.phrsWalletAddress!);

      // 验证总余额
      await testWallet.reload();
      const expectedTotal = amounts.reduce((sum, amount) => 
        sum.plus(new BigNumber(amount)), new BigNumber(0)
      ).toFixed(3);
      
      expect(testWallet.phrsBalance).toBe(expectedTotal);
    });
  });
});
