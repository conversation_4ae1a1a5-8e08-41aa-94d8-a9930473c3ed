// src/tests/setup.ts
import { sequelize } from '../config/db';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS = '0xcontract1234567890123456789012345678901234';
process.env.PHAROS_RPC_URL = 'http://test-rpc';

// 全局测试设置
beforeAll(async () => {
  // 确保数据库连接
  try {
    await sequelize.authenticate();
    console.log('Test database connection established.');
  } catch (error) {
    console.error('Unable to connect to test database:', error);
    process.exit(1);
  }
});

// 全局测试清理
afterAll(async () => {
  try {
    await sequelize.close();
    console.log('Test database connection closed.');
  } catch (error) {
    console.error('Error closing test database connection:', error);
  }
});

// 增加Jest超时时间
jest.setTimeout(30000);

// Mock console.log in tests to reduce noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeEach(() => {
  // 在测试中静默console.log，但保留console.error
  console.log = jest.fn();
});

afterEach(() => {
  // 恢复console.log
  console.log = originalConsoleLog;
});
