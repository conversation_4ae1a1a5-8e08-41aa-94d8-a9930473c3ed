// src/tests/integration/phrsSystem.integration.test.ts
import request from 'supertest';
import { app } from '../../app';
import { sequelize } from '../../config/db';
import { User, UserWallet, IapProduct, IapPurchase, PhrsDeposit, Booster } from '../../models';
import { generateTestToken } from '../helpers/authHelper';
import BigNumber from 'bignumber.js';

describe('PHRS System Integration Tests', () => {
  let testUser: User;
  let testWallet: UserWallet;
  let speedBoostProduct: IapProduct;
  let timeWarpProduct: IapProduct;
  let authToken: string;

  beforeAll(async () => {
    await sequelize.authenticate();
  });

  beforeEach(async () => {
    // 清理所有相关数据
    await Booster.destroy({ where: {}, force: true });
    await IapPurchase.destroy({ where: {}, force: true });
    await PhrsDeposit.destroy({ where: {}, force: true });
    await IapProduct.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });

    // 创建测试用户
    testUser = await User.create({
      telegramId: '123456789',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User'
    });

    // 创建测试钱包，初始PHRS余额为1000
    testWallet = await UserWallet.create({
      userId: testUser.id,
      walletAddress: '******************************************',
      phrsBalance: '1000.000',
      phrsWalletAddress: '******************************************',
      gem: '100.000',
      milk: 50
    });

    // 创建测试商品
    speedBoostProduct = await IapProduct.create({
      productId: 'speed_boost_2x_1h',
      name: '2x Speed Boost (1 Hour)',
      type: 'speed_boost',
      priceUsd: 4.99,
      priceKaia: 10.0,
      multiplier: 2,
      duration: 3600,
      quantity: 1,
      dailyLimit: 3,
      isActive: true
    });

    timeWarpProduct = await IapProduct.create({
      productId: 'time_warp_24h',
      name: 'Time Warp (24 Hours)',
      type: 'time_warp',
      priceUsd: 19.99,
      priceKaia: 40.0,
      duration: 86400,
      quantity: 1,
      dailyLimit: 1,
      isActive: true
    });

    authToken = generateTestToken(testUser.id, testWallet.id);
  });

  afterEach(async () => {
    await Booster.destroy({ where: {}, force: true });
    await IapPurchase.destroy({ where: {}, force: true });
    await PhrsDeposit.destroy({ where: {}, force: true });
    await IapProduct.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('完整的PHRS充值到购买流程', () => {
    it('应该完成从充值到购买的完整流程', async () => {
      // 1. 绑定PHRS钱包地址
      const bindResponse = await request(app)
        .post('/api/phrs-deposit/bind-wallet')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          phrsWalletAddress: '******************************************'
        });

      expect(bindResponse.status).toBe(200);

      // 2. 模拟充值记录
      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: '******************************************',
        amount: '500.000',
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      // 3. 同步余额
      const syncResponse = await request(app)
        .post('/api/phrs-deposit/sync-balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(syncResponse.status).toBe(200);

      // 4. 检查余额
      const balanceResponse = await request(app)
        .get('/api/phrs-payment/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(balanceResponse.status).toBe(200);
      expect(balanceResponse.body.data.phrsBalance).toBe('500.000');

      // 5. 购买速度提升道具
      const purchaseResponse = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(purchaseResponse.status).toBe(200);
      expect(purchaseResponse.body.data.phrsPaid).toBe('10.000');
      expect(purchaseResponse.body.data.remainingBalance).toBe('490.000');

      // 6. 验证购买记录
      const purchase = await IapPurchase.findOne({
        where: { walletId: testWallet.id }
      });
      expect(purchase).toBeTruthy();
      expect(purchase!.paymentMethod).toBe('phrs');
      expect(purchase!.status).toBe('FINALIZED');

      // 7. 验证道具已添加到背包
      const booster = await Booster.findOne({
        where: { walletId: testWallet.id }
      });
      expect(booster).toBeTruthy();
      expect(booster!.type).toBe('speed_boost');
      expect(booster!.quantity).toBe(1);

      // 8. 验证钱包余额更新
      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe('490.000');
    });
  });

  describe('多商品购买测试', () => {
    it('应该支持购买多个不同类型的商品', async () => {
      // 购买速度提升
      const purchase1Response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(purchase1Response.status).toBe(200);

      // 购买时间跳跃
      const purchase2Response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: timeWarpProduct.id
        });

      expect(purchase2Response.status).toBe(200);

      // 验证总花费
      const expectedSpent = new BigNumber('10.000').plus(new BigNumber('40.000'));
      const expectedBalance = new BigNumber('1000.000').minus(expectedSpent);

      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe(expectedBalance.toFixed(3));

      // 验证购买记录
      const purchases = await IapPurchase.findAll({
        where: { walletId: testWallet.id }
      });
      expect(purchases).toHaveLength(2);

      // 验证道具
      const boosters = await Booster.findAll({
        where: { walletId: testWallet.id }
      });
      expect(boosters).toHaveLength(1); // 时间跳跃会立即使用，不会存储为道具
    });
  });

  describe('购买限制集成测试', () => {
    it('应该正确执行每日购买限制', async () => {
      // 第一次购买速度提升 - 应该成功
      const response1 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response1.status).toBe(200);

      // 第二次购买速度提升 - 应该成功（每日限制为3）
      const response2 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response2.status).toBe(200);

      // 第三次购买速度提升 - 应该成功
      const response3 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response3.status).toBe(200);

      // 第四次购买速度提升 - 应该失败（超过每日限制）
      const response4 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response4.status).toBe(400);
      expect(response4.body.message).toContain('limit');
    });

    it('应该正确处理余额不足的情况', async () => {
      // 设置余额不足
      await testWallet.update({ phrsBalance: '5.000' });

      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('insufficient');
      expect(response.body.data.required).toBe('10.000');
      expect(response.body.data.available).toBe('5.000');
    });
  });

  describe('用户信息集成测试', () => {
    it('应该在用户信息中包含PHRS相关数据', async () => {
      // 创建一些充值和购买记录
      await PhrsDeposit.create({
        walletId: testWallet.id,
        userAddress: testWallet.phrsWalletAddress!,
        amount: '200.000',
        transactionHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        blockNumber: 1000,
        blockTimestamp: new Date(),
        contractAddress: '0xcontract1234567890123456789012345678901234',
        status: 'CONFIRMED',
        confirmations: 12
      });

      await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      // 获取用户信息
      const response = await request(app)
        .get('/api/user/me')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.phrsBalance).toBeDefined();
      expect(response.body.phrsWalletAddress).toBeDefined();
      expect(response.body.phrsStats).toBeDefined();
      expect(response.body.phrsStats.totalDeposits).toBe(1);
      expect(response.body.phrsStats.totalPurchases).toBe(1);
    });
  });

  describe('错误处理集成测试', () => {
    it('应该正确处理无效的商品ID', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: 99999
        });

      expect(response.status).toBe(404);
      expect(response.body.ok).toBe(false);
    });

    it('应该正确处理未认证的请求', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .send({
          productId: speedBoostProduct.id
        });

      expect(response.status).toBe(401);
      expect(response.body.ok).toBe(false);
    });

    it('应该正确处理非活跃商品', async () => {
      await speedBoostProduct.update({ isActive: false });

      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response.status).toBe(404);
      expect(response.body.ok).toBe(false);
    });
  });

  describe('BigNumber精度集成测试', () => {
    it('应该在整个系统中保持数值精度', async () => {
      // 设置精确的余额
      const preciseBalance = '123.456';
      const precisePrice = '10.123';
      
      await testWallet.update({ phrsBalance: preciseBalance });
      await speedBoostProduct.update({ priceKaia: precisePrice });

      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: speedBoostProduct.id
        });

      expect(response.status).toBe(200);

      const expectedBalance = new BigNumber(preciseBalance)
        .minus(new BigNumber(precisePrice))
        .toFixed(3);

      expect(response.body.data.remainingBalance).toBe(expectedBalance);

      // 验证数据库中的余额
      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe(expectedBalance);
    });
  });

  describe('并发购买测试', () => {
    it('应该正确处理并发购买请求', async () => {
      // 创建多个并发购买请求
      const purchasePromises = Array(3).fill(null).map(() =>
        request(app)
          .post('/api/phrs-payment/purchase')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            productId: speedBoostProduct.id
          })
      );

      const responses = await Promise.all(purchasePromises);

      // 所有请求都应该成功（在每日限制内）
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // 验证最终余额
      const expectedSpent = new BigNumber('10.000').multipliedBy(3);
      const expectedBalance = new BigNumber('1000.000').minus(expectedSpent);

      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe(expectedBalance.toFixed(3));

      // 验证购买记录数量
      const purchases = await IapPurchase.count({
        where: { walletId: testWallet.id }
      });
      expect(purchases).toBe(3);
    });
  });
});
