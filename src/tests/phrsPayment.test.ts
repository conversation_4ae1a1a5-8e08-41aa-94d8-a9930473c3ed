// src/tests/phrsPayment.test.ts
import request from 'supertest';
import { app } from '../app';
import { sequelize } from '../config/db';
import { User, UserWallet, IapProduct, IapPurchase, PhrsDeposit } from '../models';
import { generateTestToken } from './helpers/authHelper';
import BigNumber from 'bignumber.js';

describe('PHRS Payment API', () => {
  let testUser: User;
  let testWallet: UserWallet;
  let testProduct: IapProduct;
  let authToken: string;

  beforeAll(async () => {
    // 确保数据库连接
    await sequelize.authenticate();
  });

  beforeEach(async () => {
    // 清理测试数据
    await IapPurchase.destroy({ where: {}, force: true });
    await PhrsDeposit.destroy({ where: {}, force: true });
    await IapProduct.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });

    // 创建测试用户
    testUser = await User.create({
      telegramId: '123456789',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User'
    });

    // 创建测试钱包
    testWallet = await UserWallet.create({
      userId: testUser.id,
      walletAddress: '******************************************',
      phrsBalance: '100.000',
      phrsWalletAddress: '******************************************',
      gem: '0',
      milk: 0
    });

    // 创建测试商品
    testProduct = await IapProduct.create({
      productId: 'test_speed_boost',
      name: 'Test Speed Boost',
      type: 'speed_boost',
      priceUsd: 4.99,
      priceKaia: 10.0,
      multiplier: 2,
      duration: 3600,
      quantity: 1,
      isActive: true
    });

    // 生成认证token
    authToken = generateTestToken(testUser.id, testWallet.id);
  });

  afterEach(async () => {
    // 清理测试数据
    await IapPurchase.destroy({ where: {}, force: true });
    await PhrsDeposit.destroy({ where: {}, force: true });
    await IapProduct.destroy({ where: {}, force: true });
    await UserWallet.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/phrs-payment/purchase', () => {
    it('应该成功使用PHRS购买商品', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.productName).toBe(testProduct.name);
      expect(response.body.data.phrsPaid).toBe('10.000');

      // 验证余额扣减
      await testWallet.reload();
      expect(testWallet.phrsBalance).toBe('90.000');

      // 验证购买记录
      const purchase = await IapPurchase.findOne({
        where: { walletId: testWallet.id }
      });
      expect(purchase).toBeTruthy();
      expect(purchase!.paymentMethod).toBe('phrs');
      expect(purchase!.status).toBe('FINALIZED');
    });

    it('应该拒绝余额不足的购买', async () => {
      // 设置余额不足
      await testWallet.update({ phrsBalance: '5.000' });

      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response.status).toBe(400);
      expect(response.body.ok).toBe(false);
      expect(response.body.message).toContain('insufficient');
    });

    it('应该拒绝无效的商品ID', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: 99999
        });

      expect(response.status).toBe(404);
      expect(response.body.ok).toBe(false);
    });

    it('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .send({
          productId: testProduct.id
        });

      expect(response.status).toBe(401);
      expect(response.body.ok).toBe(false);
    });

    it('应该拒绝缺少参数的请求', async () => {
      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.ok).toBe(false);
    });
  });

  describe('GET /api/phrs-payment/balance', () => {
    it('应该返回用户PHRS余额信息', async () => {
      // 创建一些购买记录
      await IapPurchase.create({
        walletId: testWallet.id,
        productId: testProduct.id,
        paymentId: 'phrs_test_123',
        paymentMethod: 'phrs',
        amount: 10.0,
        currency: 'PHRS',
        status: 'FINALIZED',
        statusChecked: true,
        purchaseDate: new Date()
      });

      const response = await request(app)
        .get('/api/phrs-payment/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.phrsBalance).toBe('100.000');
      expect(response.body.data.phrsWalletAddress).toBe(testWallet.phrsWalletAddress);
      expect(response.body.data.recentPurchases).toHaveLength(1);
    });

    it('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/phrs-payment/balance');

      expect(response.status).toBe(401);
      expect(response.body.ok).toBe(false);
    });
  });

  describe('GET /api/phrs-payment/products', () => {
    it('应该返回支持PHRS支付的商品列表', async () => {
      // 创建另一个商品
      await IapProduct.create({
        productId: 'test_time_warp',
        name: 'Test Time Warp',
        type: 'time_warp',
        priceUsd: 9.99,
        priceKaia: 20.0,
        duration: 7200,
        quantity: 1,
        isActive: true
      });

      const response = await request(app)
        .get('/api/phrs-payment/products')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.ok).toBe(true);
      expect(response.body.data.products).toHaveLength(2);
      
      const product = response.body.data.products.find((p: any) => p.id === testProduct.id);
      expect(product.name).toBe(testProduct.name);
      expect(product.phrsPrice).toBe(testProduct.priceKaia);
    });

    it('应该只返回活跃的商品', async () => {
      // 创建一个非活跃商品
      await IapProduct.create({
        productId: 'inactive_product',
        name: 'Inactive Product',
        type: 'speed_boost',
        priceKaia: 15.0,
        isActive: false
      });

      const response = await request(app)
        .get('/api/phrs-payment/products')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.products).toHaveLength(1);
      expect(response.body.data.products[0].name).toBe(testProduct.name);
    });
  });

  describe('购买限制测试', () => {
    it('应该检查每日购买限制', async () => {
      // 设置商品每日限制为1
      await testProduct.update({ dailyLimit: 1 });

      // 第一次购买应该成功
      const response1 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response1.status).toBe(200);

      // 第二次购买应该失败
      const response2 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response2.status).toBe(400);
      expect(response2.body.message).toContain('limit');
    });

    it('应该检查账号购买限制', async () => {
      // 设置商品账号限制为1
      await testProduct.update({ accountLimit: 1 });

      // 第一次购买应该成功
      const response1 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response1.status).toBe(200);

      // 增加余额
      await testWallet.update({ phrsBalance: '200.000' });

      // 第二次购买应该失败
      const response2 = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response2.status).toBe(400);
      expect(response2.body.message).toContain('limit');
    });
  });

  describe('BigNumber精度测试', () => {
    it('应该正确处理大数值的PHRS余额', async () => {
      // 设置一个很大的余额
      const largeBalance = '999999999999999999999.123';
      await testWallet.update({ phrsBalance: largeBalance });

      const response = await request(app)
        .get('/api/phrs-payment/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.phrsBalance).toBe(largeBalance);
    });

    it('应该正确计算余额扣减', async () => {
      const initialBalance = '123.456';
      const productPrice = '10.123';
      
      await testWallet.update({ phrsBalance: initialBalance });
      await testProduct.update({ priceKaia: productPrice });

      const response = await request(app)
        .post('/api/phrs-payment/purchase')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          productId: testProduct.id
        });

      expect(response.status).toBe(200);
      
      const expectedBalance = new BigNumber(initialBalance)
        .minus(new BigNumber(productPrice))
        .toFixed(3);
      
      expect(response.body.data.remainingBalance).toBe(expectedBalance);
    });
  });
});
