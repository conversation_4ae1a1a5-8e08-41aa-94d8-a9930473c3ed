// src/services/phrsDepositService.ts
import { ethers } from 'ethers';
import { UserWallet, PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import BigNumber from 'bignumber.js';

/**
 * PHRS充值服务
 * 负责监听区块链事件并处理PHRS代币充值
 */
export class PhrsDepositService {
  public provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isListening: boolean = false;
  private lastProcessedBlock: number = 0;
  private readonly historicalStartBlock: number;

  // 合约ABI - 新版本合约的ABI
  private readonly contractABI = [
    "event Deposit(address indexed user, uint256 amount, uint256 timestamp)",
    "function getBalance() external view returns (uint256)",
    "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256, bool)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || '';

    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    // 设置历史事件处理的起始区块（可通过环境变量配置）
    this.historicalStartBlock = parseInt(process.env.PHRS_HISTORICAL_START_BLOCK || '0');

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);

    console.log(`PHRS充值服务初始化完成 - 合约地址: ${this.contractAddress}`);
    console.log(`历史事件处理起始区块: ${this.historicalStartBlock}`);
  }

  /**
   * 开始监听充值事件
   */
  public async startListening(): Promise<void> {
    if (this.isListening) {
      console.log('PHRS充值监听服务已在运行');
      return;
    }

    try {
      // 获取最后处理的区块号
      await this.loadLastProcessedBlock();

      console.log(`开始监听PHRS充值事件，从区块 ${this.lastProcessedBlock} 开始`);

      // 先设置监听状态为true
      this.isListening = true;

      // 使用轮询模式监听充值事件（避免eth_newFilter问题）
      this.startPolling();

      // 处理历史事件（如果有遗漏的）
      await this.processHistoricalEvents();

      console.log('✅ PHRS充值监听服务启动成功');
      
    } catch (error) {
      console.error('❌ PHRS充值监听服务启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止监听充值事件
   */
  public async stopListening(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    // 轮询模式不需要移除监听器，只需要设置标志位
    this.isListening = false;
    console.log('PHRS充值监听服务已停止');
  }

  /**
   * 开始轮询检查新的充值事件
   */
  private startPolling(): void {
    const pollInterval = 10000; // 10秒轮询一次

    console.log(`🔄 启动轮询，间隔: ${pollInterval/1000}秒`);

    const poll = async () => {
      // 双重检查确保服务仍在运行
      if (!this.isListening) {
        console.log('⏹️  轮询已停止');
        return;
      }

      try {
        console.log(`🔍 执行轮询检查... (${new Date().toLocaleTimeString()})`);
        await this.checkForNewDeposits();
      } catch (error) {
        console.error('轮询检查充值事件时出错:', error);

        // 如果是网络错误，等待更长时间再重试
        if (error instanceof Error && (
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNREFUSED')
        )) {
          console.log('🌐 检测到网络问题，延长轮询间隔');
          setTimeout(poll, pollInterval * 3); // 网络错误时延长3倍间隔
          return;
        }
      }

      // 再次检查服务状态，防止在处理过程中被停止
      if (this.isListening) {
        setTimeout(poll, pollInterval);
      } else {
        console.log('⏹️  轮询在处理过程中被停止');
      }
    };

    // 立即执行一次，然后开始定时轮询
    poll();
  }

  /**
   * 检查网络连接
   */
  private async checkNetworkConnection(): Promise<boolean> {
    try {
      await this.provider.getBlockNumber();
      return true;
    } catch (error) {
      console.error('🌐 网络连接检查失败:', error);
      return false;
    }
  }

  /**
   * 带重试的网络操作
   */
  private async withRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`🔄 操作失败，第 ${attempt}/${maxRetries} 次重试: ${lastError.message}`);

        if (attempt < maxRetries) {
          // 指数退避：1秒、2秒、4秒
          const delay = Math.pow(2, attempt - 1) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * 检查新的充值事件
   */
  private async checkForNewDeposits(): Promise<void> {
    try {
      // 使用重试机制获取当前区块
      const currentBlock = await this.withRetry(() => this.provider.getBlockNumber());
      console.log(`📊 当前区块: ${currentBlock}, 最后处理区块: ${this.lastProcessedBlock}`);

      if (currentBlock <= this.lastProcessedBlock) {
        console.log('⏭️  没有新区块需要处理');
        return; // 没有新区块
      }

      const fromBlock = this.lastProcessedBlock + 1;

      // 确保查询范围不超过当前最新区块，且不超过RPC节点限制（10,000个区块）
      const maxBlockRange = 5000; // 设置为5000个区块，留有安全余量
      const toBlock = Math.min(currentBlock, fromBlock + maxBlockRange - 1);

      console.log(`🔍 查询区块范围: ${fromBlock} 到 ${toBlock} (当前最新: ${currentBlock})`);

      // 验证区块范围的有效性
      if (fromBlock > currentBlock) {
        console.log('⚠️  起始区块超过当前区块，跳过查询');
        return;
      }

      if (fromBlock > toBlock) {
        console.log('⚠️  起始区块大于结束区块，跳过查询');
        return;
      }

      // 查询从上次处理的区块到指定区块的事件
      const filter = this.contract.filters.Deposit();
      console.log(`🔧 使用过滤器查询事件...`);

      const events = await this.contract.queryFilter(
        filter,
        fromBlock,
        toBlock
      );

      console.log(`📡 查询结果: 发现 ${events.length} 个充值事件`);

      if (events.length > 0) {
        console.log(`✅ 检查区块 ${fromBlock} 到 ${currentBlock}，发现 ${events.length} 个充值事件`);

        // 显示事件详情
        events.forEach((event, index) => {
          if ('args' in event && event.args) {
            console.log(`   事件 ${index + 1}: 区块 ${event.blockNumber}, 交易 ${event.transactionHash}`);
            console.log(`      用户: ${event.args[0]}, 金额: ${ethers.formatEther(event.args[1])} PHRS`);
          }
        });
      } else {
        console.log(`ℹ️  区块 ${fromBlock} 到 ${currentBlock} 中没有充值事件`);
      }

      // 处理每个事件，记录成功和失败的数量
      let successCount = 0;
      let failureCount = 0;
      const failedEvents: any[] = [];

      for (const event of events) {
        try {
          // 确保是EventLog类型
          if ('args' in event && event.args && event.args.length >= 3) {
            const eventLog = event as ethers.EventLog;
            console.log(`🔄 处理事件: 区块 ${eventLog.blockNumber}, 交易 ${eventLog.transactionHash}`);

            // 调用现有的事件处理方法
            await this.handleDepositEvent(
              eventLog.args[0] as string,
              eventLog.args[1] as bigint,
              eventLog.args[2] as bigint,
              BigInt(0), // depositId，新版本事件没有这个字段
              eventLog
            );

            console.log(`✅ 事件处理完成: 区块 ${eventLog.blockNumber}`);
            successCount++;
          }
        } catch (error) {
          console.error('处理单个充值事件时出错:', error);
          failureCount++;
          failedEvents.push({ event, error });
        }
      }

      // 只有在所有事件都成功处理时才更新区块号
      if (failureCount === 0) {
        this.lastProcessedBlock = toBlock;
        await this.saveLastProcessedBlock();
        console.log(`📝 更新最后处理区块号: ${toBlock}`);
      } else {
        console.warn(`⚠️  有 ${failureCount} 个事件处理失败，不更新区块号`);
        console.warn(`   成功处理: ${successCount} 个事件`);
        console.warn(`   失败事件将在下次轮询时重试`);

        // 记录失败的事件详情
        failedEvents.forEach((failed, index) => {
          console.warn(`   失败事件 ${index + 1}: ${failed.error.message}`);
        });
      }

    } catch (error) {
      console.error('检查新充值事件时出错:', error);
      console.error('错误详情:', error);
    }
  }

  /**
   * 处理充值事件
   */
  private async handleDepositEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    depositId: bigint = BigInt(0),
    event: ethers.EventLog
  ): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      console.log(`收到PHRS充值事件:`, {
        user,
        amount: ethers.formatEther(amount),
        timestamp: Number(timestamp),
        depositId: Number(depositId),
        txHash: event.transactionHash,
        blockNumber: event.blockNumber
      });

      // 使用行锁检查是否已处理过此交易（防止并发处理）
      const existingDeposit = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash },
        lock: transaction.LOCK.UPDATE, // 添加行锁
        transaction
      });

      if (existingDeposit) {
        console.log(`交易 ${event.transactionHash} 已处理过，跳过`);
        await transaction.rollback();
        return;
      }

      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: user.toLowerCase() },
        transaction
      });

      if (!userWallet) {
        console.log(`未找到地址 ${user} 对应的用户钱包，创建充值记录但不更新余额`);

        // 直接创建记录，如果重复会被 catch 捕获
        try {
          await PhrsDeposit.create({
            walletId: null, // 明确设置为null，表示未注册用户
            userAddress: user.toLowerCase(),
            amount: ethers.formatEther(amount),
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            blockTimestamp: new Date(Number(timestamp) * 1000),
            contractAddress: this.contractAddress,
            status: 'FAILED',
            confirmations: 1,
            processedAt: new Date(),
            errorMessage: 'User wallet not found'
          }, { transaction });

          await transaction.commit();
          console.log(`✅ 已记录未注册用户的充值失败: ${event.transactionHash}`);
          return; // 成功处理，直接返回
        } catch (createError: any) {
          // 安全地回滚事务
          try {
            await transaction.rollback();
          } catch (rollbackError: any) {
            // 如果事务已经被回滚或提交，忽略错误
            if (!rollbackError.message.includes('finished')) {
              console.warn('未注册用户事务回滚时出错:', rollbackError);
            }
          }

          // 如果是重复键错误，说明记录已存在，这是正常的
          if (createError.name === 'SequelizeUniqueConstraintError') {
            console.log(`⚠️  充值记录已存在，跳过: ${event.transactionHash}`);
            return; // 重复记录，正常返回
          } else {
            // 其他错误，使用新事务记录失败
            console.error('未注册用户记录创建失败:', createError);

            // 使用新的事务记录失败
            const failureTransaction = await sequelize.transaction();
            try {
              const existingRecord = await PhrsDeposit.findOne({
                where: { transactionHash: event.transactionHash },
                transaction: failureTransaction
              });

              if (!existingRecord) {
                await PhrsDeposit.create({
                  walletId: null,
                  userAddress: user.toLowerCase(),
                  amount: ethers.formatEther(amount),
                  transactionHash: event.transactionHash,
                  blockNumber: event.blockNumber,
                  blockTimestamp: new Date(Number(timestamp) * 1000),
                  contractAddress: this.contractAddress,
                  status: 'FAILED',
                  confirmations: 0,
                  errorMessage: `Create failed: ${createError.message}`
                }, { transaction: failureTransaction });

                await failureTransaction.commit();
                console.log(`📝 已使用新事务记录失败充值: ${event.transactionHash}`);
              } else {
                await failureTransaction.rollback();
                console.log(`⚠️  失败充值记录已存在，跳过: ${event.transactionHash}`);
              }
            } catch (failureError: any) {
              try {
                await failureTransaction.rollback();
              } catch (rollbackError: any) {
                if (!rollbackError.message.includes('finished')) {
                  console.warn('失败记录事务回滚时出错:', rollbackError);
                }
              }
              console.error('记录失败充值时出错:', failureError);
            }
            return; // 处理完成，返回
          }
        }
      }

      // 使用BigNumber处理金额计算
      const depositAmount = new BigNumber(ethers.formatEther(amount));
      const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');
      const newBalance = currentBalance.plus(depositAmount);

      // 更新用户PHRS余额
      await userWallet.update({
        phrsBalance: newBalance.toFixed(3),
        phrsWalletAddress: user.toLowerCase(),
        lastPhrsUpdateTime: new Date()
      }, { transaction });

      // 创建充值记录
      await PhrsDeposit.create({
        walletId: userWallet.id,
        userAddress: user.toLowerCase(),
        amount: depositAmount.toFixed(3),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockTimestamp: new Date(Number(timestamp) * 1000),
        contractAddress: this.contractAddress,
        status: 'CONFIRMED',
        confirmations: 1,
        processedAt: new Date()
      }, { transaction });

      await transaction.commit();
      
      console.log(`✅ PHRS充值处理成功:`, {
        walletId: userWallet.id,
        user,
        amount: depositAmount.toFixed(3),
        newBalance: newBalance.toFixed(3),
        txHash: event.transactionHash
      });

    } catch (error) {
      // 安全地回滚事务
      try {
        await transaction.rollback();
      } catch (rollbackError: any) {
        // 如果事务已经被回滚或提交，忽略错误
        if (!rollbackError.message.includes('finished')) {
          console.warn('事务回滚时出错:', rollbackError);
        }
      }

      console.error('❌ 处理PHRS充值事件失败:', error);

      // 使用新的事务记录失败的充值
      const failureTransaction = await sequelize.transaction();
      try {
        // 先检查是否已存在记录
        const existingRecord = await PhrsDeposit.findOne({
          where: { transactionHash: event.transactionHash },
          lock: failureTransaction.LOCK.UPDATE, // 添加行锁
          transaction: failureTransaction
        });

        if (!existingRecord) {
          await PhrsDeposit.create({
            walletId: null, // 设置为null表示处理失败
            userAddress: user.toLowerCase(),
            amount: ethers.formatEther(amount),
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            blockTimestamp: new Date(Number(timestamp) * 1000),
            contractAddress: this.contractAddress,
            status: 'FAILED',
            confirmations: 0,
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }, { transaction: failureTransaction });

          await failureTransaction.commit();
          console.log(`📝 已记录失败充值: ${event.transactionHash}`);
        } else {
          await failureTransaction.rollback();
          console.log(`⚠️  失败充值记录已存在，跳过: ${event.transactionHash}`);
        }
      } catch (recordError: any) {
        try {
          await failureTransaction.rollback();
        } catch (rollbackError: any) {
          // 如果事务已经被回滚或提交，忽略错误
          if (!rollbackError.message.includes('finished')) {
            console.warn('失败记录事务回滚时出错:', rollbackError);
          }
        }

        // 如果是重复键错误，忽略它
        if (recordError.name === 'SequelizeUniqueConstraintError') {
          console.log(`⚠️  充值记录已存在，跳过重复插入: ${event.transactionHash}`);
        } else {
          console.error('记录失败充值时出错:', recordError);
        }
      }
    }
  }

  /**
   * 处理历史事件 - 分批处理所有未处理的历史交易
   */
  private async processHistoricalEvents(): Promise<void> {
    try {
      const currentBlock = await this.provider.getBlockNumber();

      // 确定起始区块：取最后处理区块+1 和 配置的历史起始区块 中的较大值
      let fromBlock = Math.max(this.lastProcessedBlock + 1, this.historicalStartBlock);

      if (fromBlock >= currentBlock) {
        console.log('没有需要处理的历史事件');
        return;
      }

      console.log(`🔍 开始处理历史PHRS充值事件，从区块 ${fromBlock} 到 ${currentBlock}`);
      console.log(`   配置的历史起始区块: ${this.historicalStartBlock}`);
      console.log(`   最后处理区块: ${this.lastProcessedBlock}`);

      const maxBlockRange = 5000; // 每批处理5000个区块
      let totalProcessedEvents = 0;
      let batchCount = 0;

      // 分批处理历史事件
      while (fromBlock < currentBlock) {
        batchCount++;
        const toBlock = Math.min(fromBlock + maxBlockRange - 1, currentBlock);

        console.log(`📦 处理批次 ${batchCount}: 区块 ${fromBlock} 到 ${toBlock}`);

        try {
          // 使用重试机制查询事件
          const filter = this.contract.filters.Deposit();
          const events = await this.withRetry(() =>
            this.contract.queryFilter(filter, fromBlock, toBlock)
          );

          console.log(`   找到 ${events.length} 个充值事件`);

          // 处理这批事件，记录成功和失败数量
          let batchSuccessCount = 0;
          let batchFailureCount = 0;

          for (const event of events) {
            if ('args' in event && event.args && event.args.length >= 3) {
              const eventLog = event as ethers.EventLog;
              try {
                await this.handleDepositEvent(
                  eventLog.args[0] as string,
                  eventLog.args[1] as bigint,
                  eventLog.args[2] as bigint,
                  BigInt(0), // depositId，新版本事件没有这个字段
                  eventLog
                );
                batchSuccessCount++;
                totalProcessedEvents++;
              } catch (error) {
                console.error(`   处理事件失败 (区块 ${eventLog.blockNumber}):`, error);
                batchFailureCount++;
              }
            }
          }

          // 只有在所有事件都成功处理时才更新进度
          if (batchFailureCount === 0) {
            this.lastProcessedBlock = toBlock;
            await this.saveLastProcessedBlock();
            console.log(`   ✅ 批次 ${batchCount} 处理完成，已处理到区块 ${toBlock}`);
          } else {
            console.warn(`   ⚠️  批次 ${batchCount} 部分失败: 成功 ${batchSuccessCount}, 失败 ${batchFailureCount}`);
            console.warn(`   不更新进度，失败的事件将在下次处理时重试`);
            // 不更新 lastProcessedBlock，这样下次会重新处理这个范围
            break; // 停止处理后续批次，等待下次重试
          }

          // 如果处理的事件很多，添加短暂延迟避免过载
          if (events.length > 100) {
            console.log('   ⏳ 短暂延迟以避免系统过载...');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error) {
          console.error(`   ❌ 批次 ${batchCount} 查询失败:`, error);
          console.warn(`   停止历史事件处理，等待下次重试`);
          break; // 停止处理，等待下次重试
        }

        fromBlock = toBlock + 1;
      }

      console.log(`🎉 历史事件处理完成！`);
      console.log(`   总批次数: ${batchCount}`);
      console.log(`   总处理事件数: ${totalProcessedEvents}`);
      console.log(`   最终处理到区块: ${this.lastProcessedBlock}`);

    } catch (error) {
      console.error('❌ 处理历史事件失败:', error);
    }
  }

  /**
   * 加载最后处理的区块号
   */
  private async loadLastProcessedBlock(): Promise<void> {
    try {
      // 从数据库获取最后处理的区块号
      const lastDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      if (lastDeposit) {
        this.lastProcessedBlock = Number(lastDeposit.blockNumber);
        console.log(`从数据库加载最后处理的区块号: ${this.lastProcessedBlock}`);
      } else {
        // 如果没有记录，使用配置的历史起始区块
        if (this.historicalStartBlock > 0) {
          this.lastProcessedBlock = this.historicalStartBlock - 1; // 减1是为了让历史处理从起始区块开始
          console.log(`没有历史记录，使用配置的起始区块: ${this.historicalStartBlock}`);
        } else {
          // 如果没有配置历史起始区块，从当前区块开始
          this.lastProcessedBlock = await this.provider.getBlockNumber();
          console.log(`没有历史记录且未配置起始区块，从当前区块开始: ${this.lastProcessedBlock}`);
        }
      }

      console.log(`最后处理的区块号设置为: ${this.lastProcessedBlock}`);
    } catch (error) {
      console.error('加载最后处理区块号失败:', error);
      this.lastProcessedBlock = await this.provider.getBlockNumber();
    }
  }

  /**
   * 保存最后处理的区块号
   */
  private async saveLastProcessedBlock(): Promise<void> {
    // 这里可以将区块号保存到Redis或数据库中
    // 目前通过查询最新的充值记录来获取
    console.log(`已处理到区块: ${this.lastProcessedBlock}`);
  }

  /**
   * 手动同步指定用户的PHRS余额
   */
  public async syncUserBalance(walletAddress: string): Promise<void> {
    try {
      console.log(`开始同步用户 ${walletAddress} 的PHRS余额`);
      
      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: walletAddress.toLowerCase() }
      });

      if (!userWallet) {
        throw new Error(`未找到地址 ${walletAddress} 对应的用户钱包`);
      }

      // 获取用户的所有确认充值记录
      const deposits = await PhrsDeposit.findAll({
        where: {
          walletId: userWallet.id,
          status: 'CONFIRMED'
        }
      });

      // 计算总余额
      let totalBalance = new BigNumber(0);
      for (const deposit of deposits) {
        totalBalance = totalBalance.plus(new BigNumber(deposit.amount.toString()));
      }

      // 更新用户余额
      await userWallet.update({
        phrsBalance: totalBalance.toFixed(3),
        lastPhrsUpdateTime: new Date()
      });

      console.log(`✅ 用户 ${walletAddress} PHRS余额同步完成: ${totalBalance.toFixed(3)}`);
      
    } catch (error) {
      console.error(`❌ 同步用户 ${walletAddress} PHRS余额失败:`, error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  public getStatus(): {
    isListening: boolean;
    contractAddress: string;
    lastProcessedBlock: number;
    providerUrl: string;
  } {
    return {
      isListening: this.isListening,
      contractAddress: this.contractAddress,
      lastProcessedBlock: this.lastProcessedBlock,
      providerUrl: this.provider._getConnection().url
    };
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      networkConnection: boolean;
      databaseConnection: boolean;
      serviceRunning: boolean;
      lastProcessedBlock: number;
      currentBlock?: number;
      blockLag?: number;
    };
  }> {
    const details = {
      networkConnection: false,
      databaseConnection: false,
      serviceRunning: this.isListening,
      lastProcessedBlock: this.lastProcessedBlock,
      currentBlock: undefined as number | undefined,
      blockLag: undefined as number | undefined
    };

    try {
      // 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      details.networkConnection = true;
      details.currentBlock = currentBlock;
      details.blockLag = currentBlock - this.lastProcessedBlock;
    } catch (error) {
      console.error('健康检查 - 网络连接失败:', error);
    }

    try {
      // 检查数据库连接
      await sequelize.authenticate();
      details.databaseConnection = true;
    } catch (error) {
      console.error('健康检查 - 数据库连接失败:', error);
    }

    const isHealthy = details.networkConnection &&
                     details.databaseConnection &&
                     details.serviceRunning &&
                     (details.blockLag === undefined || details.blockLag < 100); // 区块延迟不超过100

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      details
    };
  }

  /**
   * 测试方法：手动处理指定区块范围的事件
   * @param fromBlock 起始区块号
   * @param toBlock 结束区块号（可选，默认为fromBlock）
   */
  public async testProcessBlocks(fromBlock: number, toBlock?: number): Promise<void> {
    let endBlock = toBlock || fromBlock;

    console.log(`🧪 开始测试处理区块 ${fromBlock} 到 ${endBlock}`);
    console.log('================================================');

    try {
      // 1. 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      console.log(`📡 当前网络区块: ${currentBlock}`);

      if (fromBlock > currentBlock) {
        console.log('❌ 起始区块号超过当前区块');
        return;
      }

      // 确保结束区块不超过当前区块
      if (endBlock > currentBlock) {
        console.log(`⚠️  结束区块号超过当前区块，调整为 ${currentBlock}`);
        endBlock = currentBlock;
      }

      // 确保区块范围不超过RPC节点限制
      const maxBlockRange = 5000; // 设置为5000个区块，留有安全余量
      if (endBlock - fromBlock + 1 > maxBlockRange) {
        const newEndBlock = fromBlock + maxBlockRange - 1;
        console.log(`⚠️  区块范围过大，调整结束区块为 ${newEndBlock} (最大范围: ${maxBlockRange}个区块)`);
        endBlock = newEndBlock;
      }

      // 确保fromBlock不大于toBlock
      if (fromBlock > endBlock) {
        console.log(`❌ 起始区块 ${fromBlock} 大于结束区块 ${endBlock}，无法查询`);
        return;
      }

      // 2. 查询指定区块范围的事件
      console.log(`\n🔍 查询区块 ${fromBlock} 到 ${endBlock} 的事件...`);
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, endBlock);

      console.log(`📡 找到 ${events.length} 个Deposit事件`);

      if (events.length === 0) {
        console.log('ℹ️  指定区块范围内没有充值事件');
        return;
      }

      // 3. 显示事件详情
      console.log(`\n📝 事件详情:`);
      events.forEach((event, index) => {
        if ('args' in event && event.args) {
          console.log(`   事件 ${index + 1}:`);
          console.log(`     区块: ${event.blockNumber}`);
          console.log(`     交易: ${event.transactionHash}`);
          console.log(`     用户: ${event.args[0]}`);
          console.log(`     金额: ${ethers.formatEther(event.args[1])} PHRS`);
          console.log(`     时间: ${new Date(Number(event.args[2]) * 1000).toLocaleString()}`);
        }
      });

      // 4. 处理每个事件
      console.log(`\n🔄 开始处理事件...`);
      let processedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;

      for (const event of events) {
        try {
          if ('args' in event && event.args && event.args.length >= 3) {
            const eventLog = event as ethers.EventLog;
            console.log(`\n🔄 处理事件: 区块 ${eventLog.blockNumber}, 交易 ${eventLog.transactionHash}`);

            // 调用现有的事件处理方法
            await this.handleDepositEvent(
              eventLog.args[0] as string,
              eventLog.args[1] as bigint,
              eventLog.args[2] as bigint,
              BigInt(0), // depositId，新版本事件没有这个字段
              eventLog
            );

            processedCount++;
            console.log(`✅ 事件处理完成`);
          } else {
            console.log(`⚠️  跳过无效事件: ${event.transactionHash}`);
            skippedCount++;
          }
        } catch (error) {
          console.error(`❌ 处理事件失败: ${event.transactionHash}`, error);
          errorCount++;
        }
      }

      // 5. 显示处理结果
      console.log(`\n📊 处理结果统计:`);
      console.log(`================================================`);
      console.log(`📡 总事件数: ${events.length}`);
      console.log(`✅ 成功处理: ${processedCount}`);
      console.log(`⚠️  跳过事件: ${skippedCount}`);
      console.log(`❌ 处理失败: ${errorCount}`);

      if (processedCount > 0) {
        console.log(`\n🎉 测试完成！成功处理了 ${processedCount} 个事件`);
      } else {
        console.log(`\n⚠️  没有成功处理任何事件，请检查日志`);
      }

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 测试方法：重置最后处理的区块号（用于重新处理）
   * @param blockNumber 要设置的区块号
   */
  public async testResetLastProcessedBlock(blockNumber: number): Promise<void> {
    console.log(`🔄 重置最后处理区块号: ${this.lastProcessedBlock} → ${blockNumber}`);
    this.lastProcessedBlock = blockNumber;
    await this.saveLastProcessedBlock();
    console.log(`✅ 重置完成`);
  }
}

// 导出单例实例
export const phrsDepositService = new PhrsDepositService();
