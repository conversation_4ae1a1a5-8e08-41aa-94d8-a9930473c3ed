// src/services/phrsDepositService.ts
import { ethers } from 'ethers';
import { UserWallet, PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';

/**
 * PHRS充值服务
 * 负责监听区块链事件并处理PHRS代币充值
 */
export class PhrsDepositService {
  public provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isListening: boolean = false;
  private lastProcessedBlock: number = 0;

  // 合约ABI - 只包含需要的事件和函数
  private readonly contractABI = [
    "event Deposit(address indexed user, uint256 amount, uint256 timestamp, uint256 indexed depositId)",
    "function getDeposit(uint256 index) external view returns (tuple(address user, uint256 amount, uint256 timestamp, uint256 blockNumber, bytes32 txHash))",
    "function totalDepositCount() external view returns (uint256)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://rpc.pharos.network';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || '';
    
    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);
    
    console.log(`PHRS充值服务初始化完成 - 合约地址: ${this.contractAddress}`);
  }

  /**
   * 开始监听充值事件
   */
  public async startListening(): Promise<void> {
    if (this.isListening) {
      console.log('PHRS充值监听服务已在运行');
      return;
    }

    try {
      // 获取最后处理的区块号
      await this.loadLastProcessedBlock();
      
      console.log(`开始监听PHRS充值事件，从区块 ${this.lastProcessedBlock} 开始`);
      
      // 监听新的充值事件
      this.contract.on('Deposit', this.handleDepositEvent.bind(this));
      
      // 处理历史事件（如果有遗漏的）
      await this.processHistoricalEvents();
      
      this.isListening = true;
      console.log('✅ PHRS充值监听服务启动成功');
      
    } catch (error) {
      console.error('❌ PHRS充值监听服务启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止监听充值事件
   */
  public async stopListening(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    this.contract.removeAllListeners('Deposit');
    this.isListening = false;
    console.log('PHRS充值监听服务已停止');
  }

  /**
   * 处理充值事件
   */
  private async handleDepositEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    depositId: bigint,
    event: ethers.EventLog
  ): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      console.log(`收到PHRS充值事件:`, {
        user,
        amount: ethers.formatEther(amount),
        timestamp: Number(timestamp),
        depositId: Number(depositId),
        txHash: event.transactionHash,
        blockNumber: event.blockNumber
      });

      // 检查是否已处理过此交易
      const existingDeposit = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash },
        transaction
      });

      if (existingDeposit) {
        console.log(`交易 ${event.transactionHash} 已处理过，跳过`);
        await transaction.rollback();
        return;
      }

      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: user.toLowerCase() },
        transaction
      });

      if (!userWallet) {
        console.log(`未找到地址 ${user} 对应的用户钱包，创建充值记录但不更新余额`);
        
        // 创建充值记录但标记为失败
        await PhrsDeposit.create({
          walletId: 0, // 临时设为0，表示未找到对应用户
          userAddress: user.toLowerCase(),
          amount: ethers.formatEther(amount),
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber,
          blockTimestamp: new Date(Number(timestamp) * 1000),
          contractAddress: this.contractAddress,
          status: 'FAILED',
          confirmations: 1,
          processedAt: new Date(),
          errorMessage: 'User wallet not found'
        }, { transaction });

        await transaction.commit();
        return;
      }

      // 使用BigNumber处理金额计算
      const depositAmount = new BigNumber(ethers.formatEther(amount));
      const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');
      const newBalance = currentBalance.plus(depositAmount);

      // 更新用户PHRS余额
      await userWallet.update({
        phrsBalance: newBalance.toFixed(3),
        phrsWalletAddress: user.toLowerCase(),
        lastPhrsUpdateTime: new Date()
      }, { transaction });

      // 创建充值记录
      await PhrsDeposit.create({
        walletId: userWallet.id,
        userAddress: user.toLowerCase(),
        amount: depositAmount.toFixed(3),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockTimestamp: new Date(Number(timestamp) * 1000),
        contractAddress: this.contractAddress,
        status: 'CONFIRMED',
        confirmations: 1,
        processedAt: new Date()
      }, { transaction });

      await transaction.commit();
      
      console.log(`✅ PHRS充值处理成功:`, {
        walletId: userWallet.id,
        user,
        amount: depositAmount.toFixed(3),
        newBalance: newBalance.toFixed(3),
        txHash: event.transactionHash
      });

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 处理PHRS充值事件失败:', error);
      
      // 记录失败的充值
      try {
        await PhrsDeposit.create({
          walletId: 0,
          userAddress: user.toLowerCase(),
          amount: ethers.formatEther(amount),
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber,
          blockTimestamp: new Date(Number(timestamp) * 1000),
          contractAddress: this.contractAddress,
          status: 'FAILED',
          confirmations: 0,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        });
      } catch (recordError) {
        console.error('记录失败充值时出错:', recordError);
      }
    }
  }

  /**
   * 处理历史事件
   */
  private async processHistoricalEvents(): Promise<void> {
    try {
      const currentBlock = await this.provider.getBlockNumber();
      const fromBlock = Math.max(this.lastProcessedBlock + 1, currentBlock - 1000); // 最多处理1000个区块的历史
      
      if (fromBlock >= currentBlock) {
        console.log('没有需要处理的历史事件');
        return;
      }

      console.log(`处理历史PHRS充值事件，区块范围: ${fromBlock} - ${currentBlock}`);
      
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, currentBlock);
      
      console.log(`找到 ${events.length} 个历史充值事件`);
      
      for (const event of events) {
        if (event instanceof ethers.EventLog) {
          const [user, amount, timestamp, depositId] = event.args;
          await this.handleDepositEvent(user, amount, timestamp, depositId, event);
        }
      }
      
      // 更新最后处理的区块号
      this.lastProcessedBlock = currentBlock;
      await this.saveLastProcessedBlock();
      
    } catch (error) {
      console.error('处理历史事件失败:', error);
    }
  }

  /**
   * 加载最后处理的区块号
   */
  private async loadLastProcessedBlock(): Promise<void> {
    try {
      // 从数据库获取最后处理的区块号
      const lastDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      if (lastDeposit) {
        this.lastProcessedBlock = Number(lastDeposit.blockNumber);
      } else {
        // 如果没有记录，从当前区块开始
        this.lastProcessedBlock = await this.provider.getBlockNumber();
      }
      
      console.log(`最后处理的区块号: ${this.lastProcessedBlock}`);
    } catch (error) {
      console.error('加载最后处理区块号失败:', error);
      this.lastProcessedBlock = await this.provider.getBlockNumber();
    }
  }

  /**
   * 保存最后处理的区块号
   */
  private async saveLastProcessedBlock(): Promise<void> {
    // 这里可以将区块号保存到Redis或数据库中
    // 目前通过查询最新的充值记录来获取
    console.log(`已处理到区块: ${this.lastProcessedBlock}`);
  }

  /**
   * 手动同步指定用户的PHRS余额
   */
  public async syncUserBalance(walletAddress: string): Promise<void> {
    try {
      console.log(`开始同步用户 ${walletAddress} 的PHRS余额`);
      
      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: walletAddress.toLowerCase() }
      });

      if (!userWallet) {
        throw new Error(`未找到地址 ${walletAddress} 对应的用户钱包`);
      }

      // 获取用户的所有确认充值记录
      const deposits = await PhrsDeposit.findAll({
        where: {
          walletId: userWallet.id,
          status: 'CONFIRMED'
        }
      });

      // 计算总余额
      let totalBalance = new BigNumber(0);
      for (const deposit of deposits) {
        totalBalance = totalBalance.plus(new BigNumber(deposit.amount.toString()));
      }

      // 更新用户余额
      await userWallet.update({
        phrsBalance: totalBalance.toFixed(3),
        lastPhrsUpdateTime: new Date()
      });

      console.log(`✅ 用户 ${walletAddress} PHRS余额同步完成: ${totalBalance.toFixed(3)}`);
      
    } catch (error) {
      console.error(`❌ 同步用户 ${walletAddress} PHRS余额失败:`, error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  public getStatus(): object {
    return {
      isListening: this.isListening,
      contractAddress: this.contractAddress,
      lastProcessedBlock: this.lastProcessedBlock,
      providerUrl: this.provider._getConnection().url
    };
  }
}

// 导出单例实例
export const phrsDepositService = new PhrsDepositService();
