// src/models/PhrsDeposit.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface PhrsDepositAttributes {
  id: number;
  walletId: number; // 关联的用户钱包ID
  userAddress: string; // 用户的区块链地址
  amount: number | string; // 充值金额
  transactionHash: string; // 区块链交易哈希
  blockNumber: number; // 区块号
  blockTimestamp: Date; // 区块时间戳
  contractAddress: string; // 充值合约地址
  status: 'PENDING' | 'CONFIRMED' | 'FAILED'; // 充值状态
  confirmations: number; // 确认数
  processedAt?: Date; // 处理时间
  errorMessage?: string; // 错误信息（如果失败）
  createdAt?: Date;
  updatedAt?: Date;
}

interface PhrsDepositCreationAttributes extends Optional<PhrsDepositAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class PhrsDeposit extends Model<PhrsDepositAttributes, PhrsDepositCreationAttributes> implements PhrsDepositAttributes {
  public id!: number;
  public walletId!: number;
  public userAddress!: string;
  public amount!: number | string;
  public transactionHash!: string;
  public blockNumber!: number;
  public blockTimestamp!: Date;
  public contractAddress!: string;
  public status!: 'PENDING' | 'CONFIRMED' | 'FAILED';
  public confirmations!: number;
  public processedAt?: Date;
  public errorMessage?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

PhrsDeposit.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'user_wallets',
        key: 'id'
      }
    },
    userAddress: {
      type: DataTypes.STRING(42),
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      allowNull: false,
    },
    transactionHash: {
      type: DataTypes.STRING(66),
      allowNull: false,
      unique: true, // 确保交易哈希唯一
    },
    blockNumber: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
    },
    blockTimestamp: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    contractAddress: {
      type: DataTypes.STRING(42),
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'CONFIRMED', 'FAILED'),
      allowNull: false,
      defaultValue: 'PENDING',
    },
    confirmations: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    processedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    errorMessage: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "PhrsDeposit",
    tableName: "phrs_deposits",
    timestamps: true,
    indexes: [
      {
        fields: ['walletId']
      },
      {
        fields: ['userAddress']
      },
      {
        fields: ['transactionHash'],
        unique: true
      },
      {
        fields: ['blockNumber']
      },
      {
        fields: ['status']
      },
      {
        fields: ['contractAddress']
      },
      {
        fields: ['createdAt']
      }
    ]
  }
);
