// src/routes/phrsDepositRoutes.ts
import { Router } from 'express';
import { phrsDepositController } from '../controllers/phrsDepositController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { ajv, tFromRequest, formatValidationErrors } from '../i18n';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 绑定PHRS钱包地址的验证模式
const bindPhrsWalletSchema = {
  type: 'object',
  properties: {
    phrsWalletAddress: {
      type: 'string',
      pattern: '^0x[a-fA-F0-9]{40}$'
    }
  },
  required: ['phrsWalletAddress'],
  additionalProperties: false
};

const validateBindPhrsWallet = ajv.compile(bindPhrsWalletSchema);

// 管理员操作验证模式
const toggleMonitorSchema = {
  type: 'object',
  properties: {
    action: {
      type: 'string',
      enum: ['start', 'stop']
    }
  },
  required: ['action'],
  additionalProperties: false
};

const validateToggleMonitor = ajv.compile(toggleMonitorSchema);

/**
 * 获取用户PHRS充值记录
 * GET /api/phrs-deposit/deposits
 */
router.get('/deposits', walletAuthMiddleware, async (req, res) => {
  await phrsDepositController.getUserDeposits(req as any, res);
});

/**
 * 绑定PHRS钱包地址
 * POST /api/phrs-deposit/bind-wallet
 */
router.post('/bind-wallet', walletAuthMiddleware, async (req, res) => {
  try {
    // 验证请求体
    const valid = validateBindPhrsWallet(req.body);
    if (!valid) {
      return res.status(400).json({
        ok: false,
        message: tFromRequest(req, 'errors.paramValidation'),
        details: formatValidationErrors(validateBindPhrsWallet.errors || [], req.language)
      });
    }

    await phrsDepositController.bindPhrsWallet(req as any, res);
  } catch (error: any) {
    console.error('绑定PHRS钱包地址路由错误:', error);
    res.status(500).json({
      ok: false,
      message: tFromRequest(req, 'errors.serverError'),
      details: error.message
    });
  }
});

/**
 * 手动同步PHRS余额
 * POST /api/phrs-deposit/sync-balance
 */
router.post('/sync-balance', walletAuthMiddleware, async (req, res) => {
  await phrsDepositController.syncPhrsBalance(req as any, res);
});

/**
 * 获取PHRS充值统计信息（管理员接口）
 * GET /api/phrs-deposit/admin/statistics
 */
router.get('/admin/statistics', async (req, res) => {
  // TODO: 添加管理员权限验证中间件
  await phrsDepositController.getDepositStatistics(req, res);
});

/**
 * 启动/停止监控服务（管理员接口）
 * POST /api/phrs-deposit/admin/toggle-monitor
 */
router.post('/admin/toggle-monitor', async (req, res) => {
  try {
    // TODO: 添加管理员权限验证中间件
    
    // 验证请求体
    const valid = validateToggleMonitor(req.body);
    if (!valid) {
      return res.status(400).json({
        ok: false,
        message: 'Invalid request parameters',
        details: formatValidationErrors(validateToggleMonitor.errors || [], req.language)
      });
    }

    await phrsDepositController.toggleMonitorService(req, res);
  } catch (error: any) {
    console.error('切换监控服务路由错误:', error);
    res.status(500).json({
      ok: false,
      message: 'Server error',
      details: error.message
    });
  }
});

/**
 * 健康检查接口
 * GET /api/phrs-deposit/health
 */
router.get('/health', (req, res) => {
  try {
    const { phrsDepositService } = require('../services/phrsDepositService');
    const { phrsDepositMonitor } = require('../jobs/phrsDepositMonitor');
    
    res.json({
      ok: true,
      data: {
        service: phrsDepositService.getStatus(),
        monitor: phrsDepositMonitor.getStatus(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    res.status(500).json({
      ok: false,
      message: 'Health check failed',
      details: error.message
    });
  }
});

export default router;
