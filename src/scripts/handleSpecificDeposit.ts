#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB, sequelize } from '../config/db';
import { UserWallet, PhrsDeposit } from '../models';
import readline from 'readline';

// 加载环境变量
dotenv.config();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🔧 处理特定充值记录');
  console.log('===================');

  // 区块13805521的充值信息
  const depositInfo = {
    userAddress: '******************************************',
    amount: '0.0001',
    transactionHash: '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85',
    blockNumber: 13805521,
    timestamp: 1752810922
  };

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    console.log('\n📋 充值信息:');
    console.log(`   用户地址: ${depositInfo.userAddress}`);
    console.log(`   充值金额: ${depositInfo.amount} PHRS`);
    console.log(`   交易哈希: ${depositInfo.transactionHash}`);
    console.log(`   区块号: ${depositInfo.blockNumber}`);
    console.log(`   时间: ${new Date(depositInfo.timestamp * 1000).toLocaleString()}`);

    // 1. 检查用户是否已存在
    const existingWallet = await UserWallet.findOne({
      where: { phrsWalletAddress: depositInfo.userAddress.toLowerCase() }
    });

    if (existingWallet) {
      console.log(`✅ 用户钱包已存在 (ID: ${existingWallet.id})`);
      console.log(`   当前余额: ${existingWallet.phrsBalance} PHRS`);
    } else {
      console.log(`❌ 用户钱包不存在`);
    }

    // 2. 检查充值记录是否已存在
    const existingDeposit = await PhrsDeposit.findOne({
      where: { transactionHash: depositInfo.transactionHash }
    });

    if (existingDeposit) {
      console.log(`✅ 充值记录已存在 (ID: ${existingDeposit.id})`);
      console.log(`   状态: ${existingDeposit.status}`);
      console.log(`   钱包ID: ${existingDeposit.walletId}`);
      if (existingDeposit.errorMessage) {
        console.log(`   错误信息: ${existingDeposit.errorMessage}`);
      }
    } else {
      console.log(`❌ 充值记录不存在`);
    }

    // 3. 询问处理方式
    console.log(`\n🔧 处理选项:`);
    console.log(`1. 为用户创建钱包并处理充值`);
    console.log(`2. 仅创建充值记录但不创建钱包`);
    console.log(`3. 不做任何处理`);

    const choice = await askQuestion('\n请选择处理方式 (1-3): ');

    switch (choice) {
      case '1':
        await createWalletAndProcessDeposit(depositInfo, existingWallet, existingDeposit);
        break;
      case '2':
        await createDepositRecordOnly(depositInfo, existingDeposit);
        break;
      case '3':
        console.log('👋 不做任何处理');
        break;
      default:
        console.log('❌ 无效选择');
    }

  } catch (error) {
    console.error('❌ 处理过程中发生错误:', error);
  } finally {
    rl.close();
  }

  process.exit(0);
}

async function createWalletAndProcessDeposit(depositInfo: any, existingWallet: any, existingDeposit: any) {
  console.log('\n🔄 创建用户钱包并处理充值...');
  
  const confirm = await askQuestion(`确认为地址 ${depositInfo.userAddress} 创建钱包并处理充值? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  const transaction = await sequelize.transaction();
  
  try {
    let wallet = existingWallet;

    // 创建钱包（如果不存在）
    if (!wallet) {
      wallet = await UserWallet.create({
        userId: 0, // 临时用户ID
        milk: 0, // 初始牛奶为0
        phrsWalletAddress: depositInfo.userAddress.toLowerCase(),
        phrsBalance: '0.000',
      }, { transaction });

      console.log(`✅ 创建了用户钱包 (ID: ${wallet.id})`);
    }

    // 创建或更新充值记录
    let deposit = existingDeposit;
    if (!deposit) {
      deposit = await PhrsDeposit.create({
        userAddress: depositInfo.userAddress,
        amount: depositInfo.amount,
        transactionHash: depositInfo.transactionHash,
        blockNumber: depositInfo.blockNumber,
        blockTimestamp: new Date(depositInfo.timestamp * 1000),
        walletId: wallet.id,
        status: 'CONFIRMED',
        processedAt: new Date()
      }, { transaction });

      console.log(`✅ 创建了充值记录 (ID: ${deposit.id})`);
    } else if (deposit.status === 'FAILED') {
      await deposit.update({
        walletId: wallet.id,
        status: 'CONFIRMED',
        errorMessage: null,
        processedAt: new Date()
      }, { transaction });

      console.log(`✅ 更新了充值记录状态`);
    }

    // 更新钱包余额
    const currentBalance = parseFloat((wallet.phrsBalance || '0').toString());
    const depositAmount = parseFloat(depositInfo.amount);
    const newBalance = currentBalance + depositAmount;

    await wallet.update({
      phrsBalance: newBalance.toFixed(6), // 使用6位小数精度
      lastPhrsUpdateTime: new Date()
    }, { transaction });

    console.log(`✅ 更新了钱包余额: ${currentBalance} → ${newBalance.toFixed(6)} PHRS`);

    await transaction.commit();
    
    console.log(`\n🎉 处理完成:`);
    console.log(`   钱包ID: ${wallet.id}`);
    console.log(`   充值记录ID: ${deposit.id}`);
    console.log(`   最终余额: ${newBalance.toFixed(6)} PHRS`);

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 处理失败，已回滚:', error);
  }
}

async function createDepositRecordOnly(depositInfo: any, existingDeposit: any) {
  console.log('\n🔄 仅创建充值记录...');
  
  if (existingDeposit) {
    console.log('✅ 充值记录已存在，无需创建');
    return;
  }

  const confirm = await askQuestion(`确认创建充值记录但不创建钱包? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  try {
    const deposit = await PhrsDeposit.create({
      userAddress: depositInfo.userAddress,
      amount: depositInfo.amount,
      transactionHash: depositInfo.transactionHash,
      blockNumber: depositInfo.blockNumber,
      blockTimestamp: new Date(depositInfo.timestamp * 1000),
      walletId: 0, // 表示无关联钱包
      status: 'PROCESSED_NO_WALLET',
      errorMessage: 'Processed without creating wallet',
      processedAt: new Date()
    });

    console.log(`✅ 创建了充值记录 (ID: ${deposit.id})`);
    console.log(`   状态: PROCESSED_NO_WALLET`);

  } catch (error) {
    console.error('❌ 创建充值记录失败:', error);
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
